using UnityEngine;
using System.Collections;

namespace PetingGame.Core
{
    /// <summary>
    /// Main game manager that controls the overall game flow
    /// </summary>
    public class GameManager : MonoBehaviour
    {
        [Header("Game Settings")]
        public bool autoStartBattle = true;
        public float battleTransitionDelay = 2f;
        
        [Header("References")]
        public BattleManager battleManager;
        public UIManager uiManager;
        public DataManager dataManager;
        public PlayerManager playerManager;
        
        // Game state
        public GameState currentState { get; private set; }
        public int currentLevel { get; private set; } = 1;
        public int currentStage { get; private set; } = 1;
        
        // Events
        public System.Action<GameState> OnGameStateChanged;
        public System.Action<int> OnLevelChanged;
        public System.Action<int> OnStageChanged;
        
        private static GameManager _instance;
        public static GameManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<GameManager>();
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("GameManager");
                        _instance = go.AddComponent<GameManager>();
                    }
                }
                return _instance;
            }
        }
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeGame();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }
        
        private void Start()
        {
            if (autoStartBattle)
            {
                StartCoroutine(StartFirstBattle());
            }
        }
        
        private void InitializeGame()
        {
            // Initialize all managers
            if (dataManager == null) dataManager = GetComponent<DataManager>();
            if (playerManager == null) playerManager = GetComponent<PlayerManager>();
            if (battleManager == null) battleManager = GetComponent<BattleManager>();
            if (uiManager == null) uiManager = GetComponent<UIManager>();
            
            // Load game data
            dataManager?.LoadAllData();
            
            // Initialize player
            playerManager?.InitializePlayer();
            
            ChangeGameState(GameState.MainMenu);
        }
        
        public void ChangeGameState(GameState newState)
        {
            if (currentState == newState) return;
            
            GameState previousState = currentState;
            currentState = newState;
            
            Debug.Log($"Game state changed from {previousState} to {newState}");
            OnGameStateChanged?.Invoke(newState);
            
            HandleStateChange(newState);
        }
        
        private void HandleStateChange(GameState state)
        {
            switch (state)
            {
                case GameState.MainMenu:
                    uiManager?.ShowMainMenu();
                    break;
                case GameState.Battle:
                    uiManager?.ShowBattleUI();
                    break;
                case GameState.Equipment:
                    uiManager?.ShowEquipmentMenu();
                    break;
                case GameState.Team:
                    uiManager?.ShowTeamMenu();
                    break;
                case GameState.LuckyDraw:
                    uiManager?.ShowLuckyDrawMenu();
                    break;
                case GameState.Settings:
                    uiManager?.ShowSettingsMenu();
                    break;
            }
        }
        
        public void StartBattle()
        {
            ChangeGameState(GameState.Battle);
            battleManager?.StartBattle(currentLevel, currentStage);
        }
        
        public void OnBattleComplete(bool victory, bool isBossDefeated)
        {
            if (victory)
            {
                if (isBossDefeated)
                {
                    // Move to next level
                    currentLevel++;
                    currentStage = 1;
                    OnLevelChanged?.Invoke(currentLevel);
                    Debug.Log($"Level completed! Moving to level {currentLevel}");
                }
                else
                {
                    // Move to next stage
                    currentStage++;
                    OnStageChanged?.Invoke(currentStage);
                    Debug.Log($"Stage completed! Moving to stage {currentStage}");
                }
                
                // Auto start next battle after delay
                if (autoStartBattle)
                {
                    StartCoroutine(StartNextBattleAfterDelay());
                }
            }
            else
            {
                // Handle defeat
                Debug.Log("Battle lost! Game over or retry logic here");
                ChangeGameState(GameState.MainMenu);
            }
        }
        
        private IEnumerator StartFirstBattle()
        {
            yield return new WaitForSeconds(1f);
            StartBattle();
        }
        
        private IEnumerator StartNextBattleAfterDelay()
        {
            yield return new WaitForSeconds(battleTransitionDelay);
            StartBattle();
        }
        
        public void ReturnToMainMenu()
        {
            ChangeGameState(GameState.MainMenu);
        }
        
        public void OpenEquipmentMenu()
        {
            ChangeGameState(GameState.Equipment);
        }
        
        public void OpenTeamMenu()
        {
            ChangeGameState(GameState.Team);
        }
        
        public void OpenLuckyDrawMenu()
        {
            ChangeGameState(GameState.LuckyDraw);
        }
        
        public void OpenSettingsMenu()
        {
            ChangeGameState(GameState.Settings);
        }
        
        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
            {
                // Save game data when app is paused
                dataManager?.SaveGameData();
            }
        }
        
        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
            {
                // Save game data when app loses focus
                dataManager?.SaveGameData();
            }
        }
    }
    
    public enum GameState
    {
        MainMenu,
        Battle,
        Equipment,
        Team,
        LuckyDraw,
        Settings
    }
}
