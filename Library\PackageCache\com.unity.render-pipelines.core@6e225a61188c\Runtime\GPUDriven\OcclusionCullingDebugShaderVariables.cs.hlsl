//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef OCCLUSIONCULLINGDEBUGSHADERVARIABLES_CS_HLSL
#define OCCLUSIONCULLINGDEBUGSHADERVARIABLES_CS_HLSL
// Generated from UnityEngine.Rendering.OcclusionCullingDebugShaderVariables
// PackingRules = Exact
CBUFFER_START(OcclusionCullingDebugShaderVariables)
    float4 _DepthSizeInOccluderPixels;
    uint4 _OccluderMipBounds[8];
    uint _OccluderMipLayoutSizeX;
    uint _OccluderMipLayoutSizeY;
    uint _OcclusionCullingDebugPad0;
    uint _OcclusionCullingDebugPad1;
CBUFFER_END


#endif
