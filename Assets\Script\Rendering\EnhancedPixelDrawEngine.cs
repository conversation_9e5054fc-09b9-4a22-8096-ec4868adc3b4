using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace PetingGame.Rendering
{
    /// <summary>
    /// 增強的像素繪製引擎，支援解析度適配和性能優化
    /// </summary>
    public class EnhancedPixelDrawEngine : MonoBehaviour
    {
        [Header("Performance Settings")]
        [SerializeField] private int maxCanvasPoolSize = 50;
        [SerializeField] private int maxTexturePoolSize = 100;
        [SerializeField] private bool enableBatchRendering = true;
        [SerializeField] private int batchSize = 10;
        
        [Header("Quality Settings")]
        [SerializeField] private bool adaptiveQuality = true;
        [SerializeField] private int lowEndDeviceThreshold = 2; // GB RAM
        [SerializeField] private float qualityScaleFactor = 1f;
        
        // Object pools
        private Queue<PixelCanvas> canvasPool = new Queue<PixelCanvas>();
        private Queue<Texture2D> texturePool = new Queue<Texture2D>();
        private Dictionary<int, Queue<PixelCanvas>> sizedCanvasPools = new Dictionary<int, Queue<PixelCanvas>>();
        
        // Batch rendering
        private List<PixelCanvas> batchQueue = new List<PixelCanvas>();
        private Coroutine batchRenderCoroutine;
        
        // Performance monitoring
        private float lastFrameTime;
        private int frameCount;
        private float averageFrameTime;
        
        private static EnhancedPixelDrawEngine _instance;
        public static EnhancedPixelDrawEngine Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<EnhancedPixelDrawEngine>();
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("EnhancedPixelDrawEngine");
                        _instance = go.AddComponent<EnhancedPixelDrawEngine>();
                    }
                }
                return _instance;
            }
        }
        
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeEngine();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }
        
        private void InitializeEngine()
        {
            // 檢測設備性能
            DetectDevicePerformance();
            
            // 預熱對象池
            PrewarmPools();
            
            // 啟動批次渲染
            if (enableBatchRendering)
            {
                batchRenderCoroutine = StartCoroutine(BatchRenderCoroutine());
            }
        }
        
        private void DetectDevicePerformance()
        {
            // 基於設備RAM調整品質設置
            int deviceRAM = SystemInfo.systemMemorySize;
            
            if (adaptiveQuality)
            {
                if (deviceRAM < lowEndDeviceThreshold * 1024)
                {
                    // 低端設備：降低品質
                    qualityScaleFactor = 0.75f;
                    maxCanvasPoolSize = 25;
                    batchSize = 5;
                }
                else if (deviceRAM > 4 * 1024)
                {
                    // 高端設備：提升品質
                    qualityScaleFactor = 1.25f;
                    maxCanvasPoolSize = 75;
                    batchSize = 15;
                }
            }
            
            Debug.Log($"Device Performance Detected - RAM: {deviceRAM}MB, Quality Scale: {qualityScaleFactor}");
        }
        
        private void PrewarmPools()
        {
            // 預創建常用尺寸的畫布
            int[] commonSizes = { 16, 32, 48, 64, 96, 128 };
            
            foreach (int size in commonSizes)
            {
                int poolSize = Mathf.RoundToInt(maxCanvasPoolSize / commonSizes.Length);
                Queue<PixelCanvas> sizedPool = new Queue<PixelCanvas>();
                
                for (int i = 0; i < poolSize; i++)
                {
                    PixelCanvas canvas = new PixelCanvas(size, size);
                    sizedPool.Enqueue(canvas);
                }
                
                int sizeKey = size * 1000 + size; // width * 1000 + height
                sizedCanvasPools[sizeKey] = sizedPool;
            }
        }
        
        public PixelCanvas CreateCanvas(int width, int height)
        {
            // 調整尺寸基於品質設置
            width = Mathf.RoundToInt(width * qualityScaleFactor);
            height = Mathf.RoundToInt(height * qualityScaleFactor);
            
            // 確保尺寸為偶數（像素對齊）
            width = (width + 1) & ~1;
            height = (height + 1) & ~1;
            
            int sizeKey = width * 1000 + height;
            
            // 嘗試從對應尺寸的池中獲取
            if (sizedCanvasPools.ContainsKey(sizeKey) && sizedCanvasPools[sizeKey].Count > 0)
            {
                PixelCanvas canvas = sizedCanvasPools[sizeKey].Dequeue();
                canvas.Clear();
                return canvas;
            }
            
            // 嘗試從通用池中獲取並調整尺寸
            if (canvasPool.Count > 0)
            {
                PixelCanvas canvas = canvasPool.Dequeue();
                canvas.Resize(width, height);
                return canvas;
            }
            
            // 創建新畫布
            return new PixelCanvas(width, height);
        }
        
        public void ReturnCanvas(PixelCanvas canvas)
        {
            if (canvas == null) return;
            
            canvas.Clear();
            
            int sizeKey = canvas.Width * 1000 + canvas.Height;
            
            // 優先返回到對應尺寸的池
            if (sizedCanvasPools.ContainsKey(sizeKey) && 
                sizedCanvasPools[sizeKey].Count < maxCanvasPoolSize / 6)
            {
                sizedCanvasPools[sizeKey].Enqueue(canvas);
            }
            // 否則返回到通用池
            else if (canvasPool.Count < maxCanvasPoolSize)
            {
                canvasPool.Enqueue(canvas);
            }
            // 池已滿，銷毀畫布
            else
            {
                canvas.Dispose();
            }
        }
        
        public void ExecuteDrawCommand(PixelCanvas canvas, PixelDrawCommand command)
        {
            if (canvas == null || command == null) return;
            
            // 調整繪製參數基於品質設置
            PixelDrawCommand adjustedCommand = AdjustCommandForQuality(command);
            
            // 執行繪製指令
            ExecuteDrawCommandInternal(canvas, adjustedCommand);
            
            // 如果啟用批次渲染，加入批次隊列
            if (enableBatchRendering && !batchQueue.Contains(canvas))
            {
                batchQueue.Add(canvas);
            }
            else if (!enableBatchRendering)
            {
                // 立即應用變更
                canvas.ApplyChanges();
            }
        }
        
        private PixelDrawCommand AdjustCommandForQuality(PixelDrawCommand command)
        {
            // 創建調整後的指令副本
            PixelDrawCommand adjusted = new PixelDrawCommand(
                command.CommandType,
                command.Layer,
                command.Frame,
                (int[])command.Parameters.Clone(),
                command.Color
            );
            
            // 根據品質設置調整參數
            for (int i = 0; i < adjusted.Parameters.Length; i++)
            {
                adjusted.Parameters[i] = Mathf.RoundToInt(adjusted.Parameters[i] * qualityScaleFactor);
            }
            
            return adjusted;
        }
        
        private void ExecuteDrawCommandInternal(PixelCanvas canvas, PixelDrawCommand command)
        {
            switch (command.CommandType)
            {
                case PixelDrawCommandType.SetPixel:
                    ExecuteSetPixel(canvas, command);
                    break;
                case PixelDrawCommandType.DrawLine:
                    ExecuteDrawLine(canvas, command);
                    break;
                case PixelDrawCommandType.DrawCircle:
                    ExecuteDrawCircle(canvas, command);
                    break;
                case PixelDrawCommandType.FillCircle:
                    ExecuteFillCircle(canvas, command);
                    break;
                case PixelDrawCommandType.DrawParticles:
                    ExecuteDrawParticles(canvas, command);
                    break;
                case PixelDrawCommandType.DrawGradientCircle:
                    ExecuteDrawGradientCircle(canvas, command);
                    break;
                // 添加其他繪製指令...
            }
        }
        
        private void ExecuteSetPixel(PixelCanvas canvas, PixelDrawCommand command)
        {
            if (command.Parameters.Length >= 2)
            {
                int x = command.Parameters[0];
                int y = command.Parameters[1];
                canvas.SetPixel(x, y, command.Color);
            }
        }
        
        private void ExecuteDrawLine(PixelCanvas canvas, PixelDrawCommand command)
        {
            if (command.Parameters.Length >= 4)
            {
                int x1 = command.Parameters[0];
                int y1 = command.Parameters[1];
                int x2 = command.Parameters[2];
                int y2 = command.Parameters[3];
                int thickness = command.Parameters.Length > 4 ? command.Parameters[4] : 1;
                
                DrawLineBresenham(canvas, x1, y1, x2, y2, thickness, command.Color);
            }
        }
        
        private void ExecuteDrawCircle(PixelCanvas canvas, PixelDrawCommand command)
        {
            if (command.Parameters.Length >= 3)
            {
                int centerX = command.Parameters[0];
                int centerY = command.Parameters[1];
                int radius = command.Parameters[2];
                int thickness = command.Parameters.Length > 3 ? command.Parameters[3] : 1;
                
                DrawCircleBresenham(canvas, centerX, centerY, radius, thickness, command.Color);
            }
        }
        
        private void ExecuteFillCircle(PixelCanvas canvas, PixelDrawCommand command)
        {
            if (command.Parameters.Length >= 3)
            {
                int centerX = command.Parameters[0];
                int centerY = command.Parameters[1];
                int radius = command.Parameters[2];
                
                FillCircle(canvas, centerX, centerY, radius, command.Color);
            }
        }
        
        private void ExecuteDrawParticles(PixelCanvas canvas, PixelDrawCommand command)
        {
            if (command.Parameters.Length >= 4)
            {
                int centerX = command.Parameters[0];
                int centerY = command.Parameters[1];
                int count = command.Parameters[2];
                int spread = command.Parameters[3];
                
                DrawParticles(canvas, centerX, centerY, count, spread, command.Color);
            }
        }
        
        private void ExecuteDrawGradientCircle(PixelCanvas canvas, PixelDrawCommand command)
        {
            if (command.Parameters.Length >= 3)
            {
                int centerX = command.Parameters[0];
                int centerY = command.Parameters[1];
                int radius = command.Parameters[2];
                
                DrawGradientCircle(canvas, centerX, centerY, radius, command.Color);
            }
        }
        
        // 優化的繪製算法
        private void DrawLineBresenham(PixelCanvas canvas, int x1, int y1, int x2, int y2, int thickness, Color color)
        {
            int dx = Mathf.Abs(x2 - x1);
            int dy = Mathf.Abs(y2 - y1);
            int sx = x1 < x2 ? 1 : -1;
            int sy = y1 < y2 ? 1 : -1;
            int err = dx - dy;
            
            int x = x1, y = y1;
            
            while (true)
            {
                // 繪製粗線
                for (int tx = -thickness/2; tx <= thickness/2; tx++)
                {
                    for (int ty = -thickness/2; ty <= thickness/2; ty++)
                    {
                        int pixelX = x + tx;
                        int pixelY = y + ty;
                        if (canvas.IsValidPixel(pixelX, pixelY))
                        {
                            canvas.BlendPixel(pixelX, pixelY, color, BlendMode.Alpha);
                        }
                    }
                }
                
                if (x == x2 && y == y2) break;
                
                int e2 = 2 * err;
                if (e2 > -dy)
                {
                    err -= dy;
                    x += sx;
                }
                if (e2 < dx)
                {
                    err += dx;
                    y += sy;
                }
            }
        }
        
        private void DrawCircleBresenham(PixelCanvas canvas, int centerX, int centerY, int radius, int thickness, Color color)
        {
            for (int t = 0; t < thickness; t++)
            {
                int r = radius - t;
                if (r <= 0) break;
                
                int x = 0;
                int y = r;
                int d = 3 - 2 * r;
                
                while (y >= x)
                {
                    // 繪製8個對稱點
                    SetPixelSafe(canvas, centerX + x, centerY + y, color);
                    SetPixelSafe(canvas, centerX - x, centerY + y, color);
                    SetPixelSafe(canvas, centerX + x, centerY - y, color);
                    SetPixelSafe(canvas, centerX - x, centerY - y, color);
                    SetPixelSafe(canvas, centerX + y, centerY + x, color);
                    SetPixelSafe(canvas, centerX - y, centerY + x, color);
                    SetPixelSafe(canvas, centerX + y, centerY - x, color);
                    SetPixelSafe(canvas, centerX - y, centerY - x, color);
                    
                    x++;
                    if (d > 0)
                    {
                        y--;
                        d = d + 4 * (x - y) + 10;
                    }
                    else
                    {
                        d = d + 4 * x + 6;
                    }
                }
            }
        }
        
        private void FillCircle(PixelCanvas canvas, int centerX, int centerY, int radius, Color color)
        {
            for (int y = -radius; y <= radius; y++)
            {
                for (int x = -radius; x <= radius; x++)
                {
                    if (x * x + y * y <= radius * radius)
                    {
                        int pixelX = centerX + x;
                        int pixelY = centerY + y;
                        SetPixelSafe(canvas, pixelX, pixelY, color);
                    }
                }
            }
        }
        
        private void DrawParticles(PixelCanvas canvas, int centerX, int centerY, int count, int spread, Color color)
        {
            for (int i = 0; i < count; i++)
            {
                float angle = Random.Range(0f, 2f * Mathf.PI);
                float distance = Random.Range(0f, spread);
                
                int x = centerX + Mathf.RoundToInt(Mathf.Cos(angle) * distance);
                int y = centerY + Mathf.RoundToInt(Mathf.Sin(angle) * distance);
                
                Color particleColor = color;
                particleColor.a *= Random.Range(0.5f, 1.0f);
                SetPixelSafe(canvas, x, y, particleColor);
            }
        }
        
        private void DrawGradientCircle(PixelCanvas canvas, int centerX, int centerY, int radius, Color color)
        {
            for (int y = -radius; y <= radius; y++)
            {
                for (int x = -radius; x <= radius; x++)
                {
                    float distance = Mathf.Sqrt(x * x + y * y);
                    if (distance <= radius)
                    {
                        int pixelX = centerX + x;
                        int pixelY = centerY + y;
                        
                        if (canvas.IsValidPixel(pixelX, pixelY))
                        {
                            float alpha = 1f - (distance / radius);
                            Color gradientColor = color;
                            gradientColor.a *= alpha;
                            
                            canvas.BlendPixel(pixelX, pixelY, gradientColor, BlendMode.Alpha);
                        }
                    }
                }
            }
        }
        
        private void SetPixelSafe(PixelCanvas canvas, int x, int y, Color color)
        {
            if (canvas.IsValidPixel(x, y))
            {
                canvas.SetPixel(x, y, color);
            }
        }
        
        private IEnumerator BatchRenderCoroutine()
        {
            while (true)
            {
                if (batchQueue.Count > 0)
                {
                    int processed = 0;
                    List<PixelCanvas> toProcess = new List<PixelCanvas>(batchQueue);
                    batchQueue.Clear();
                    
                    foreach (PixelCanvas canvas in toProcess)
                    {
                        if (canvas != null)
                        {
                            canvas.ApplyChanges();
                            processed++;
                            
                            // 每處理一定數量後讓出控制權
                            if (processed % batchSize == 0)
                            {
                                yield return null;
                            }
                        }
                    }
                }
                
                yield return null;
            }
        }
        
        private void Update()
        {
            // 性能監控
            frameCount++;
            float currentFrameTime = Time.unscaledDeltaTime;
            averageFrameTime = (averageFrameTime * (frameCount - 1) + currentFrameTime) / frameCount;
            
            // 動態調整品質
            if (adaptiveQuality && frameCount % 60 == 0) // 每秒檢查一次
            {
                if (averageFrameTime > 0.02f) // 低於50FPS
                {
                    qualityScaleFactor = Mathf.Max(0.5f, qualityScaleFactor * 0.95f);
                }
                else if (averageFrameTime < 0.015f) // 高於66FPS
                {
                    qualityScaleFactor = Mathf.Min(1.5f, qualityScaleFactor * 1.02f);
                }
            }
        }
        
        private void OnDestroy()
        {
            if (batchRenderCoroutine != null)
            {
                StopCoroutine(batchRenderCoroutine);
            }
            
            // 清理對象池
            while (canvasPool.Count > 0)
            {
                canvasPool.Dequeue().Dispose();
            }
            
            foreach (var pool in sizedCanvasPools.Values)
            {
                while (pool.Count > 0)
                {
                    pool.Dequeue().Dispose();
                }
            }
        }
    }
}
