//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef HDROUTPUTDEFINES_CS_HLSL
#define HDROUTPUTDEFINES_CS_HLSL
//
// UnityEngine.Rendering.HDRColorspace:  static fields
//
#define HDRCOLORSPACE_REC709 (0)
#define HDRCOLORSPACE_REC2020 (1)
#define HDRCOLORSPACE_P3D65 (2)

//
// UnityEngine.Rendering.HDREncoding:  static fields
//
#define HDRENCODING_LINEAR (3)
#define HDRENCODING_PQ (2)
#define HDRENCODING_GAMMA22 (4)
#define HDRENCODING_S_RGB (0)

//
// UnityEngine.Rendering.HDRRangeReduction:  static fields
//
#define HDRRANGEREDUCTION_NONE (0)
#define HDRRANGEREDUCTION_REINHARD (1)
#define HDRRANGEREDUCTION_BT2390 (2)
#define HDRRANGEREDUCTION_ACES1000NITS (3)
#define HDRRANGEREDUCTION_ACES2000NITS (4)
#define HDRRANGEREDUCTION_ACES4000NITS (5)


#endif
