using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using PetingGame.Data;
using PetingGame.Core;

namespace PetingGame.Battle
{
    /// <summary>
    /// Manages the battle system including turn order, actions, and battle flow
    /// </summary>
    public class BattleManager : MonoBehaviour
    {
        [Header("Battle Settings")]
        public float actionBarSpeed = 1f;
        public float battleStartDelay = 1f;
        public int maxMonstersPerBattle = 10;
        public int maxTeamSize = 5;
        
        [Header("References")]
        public Transform playerTeamParent;
        public Transform enemyTeamParent;
        public BattleUI battleUI;
        
        // Battle state
        public BattleState currentBattleState { get; private set; }
        public bool isAutoBattle { get; set; } = true;
        
        // Teams
        private List<BattleCharacter> playerTeam = new List<BattleCharacter>();
        private List<BattleCharacter> enemyTeam = new List<BattleCharacter>();
        private List<BattleCharacter> allCharacters = new List<BattleCharacter>();
        
        // Turn system
        private Queue<BattleCharacter> actionQueue = new Queue<BattleCharacter>();
        private BattleCharacter currentActor;
        
        // Battle data
        private int currentLevel;
        private int currentStage;
        private bool isBossBattle;
        
        // Events
        public System.Action<bool, bool> OnBattleComplete; // victory, isBossDefeated
        public System.Action<BattleCharacter> OnCharacterDeath;
        public System.Action<BattleCharacter, SkillData, List<BattleCharacter>> OnSkillUsed;
        public System.Action<string> OnBattleLog;
        
        private void Start()
        {
            if (battleUI == null)
                battleUI = FindObjectOfType<BattleUI>();
        }
        
        public void StartBattle(int level, int stage)
        {
            currentLevel = level;
            currentStage = stage;
            
            StartCoroutine(InitializeBattle());
        }
        
        private IEnumerator InitializeBattle()
        {
            currentBattleState = BattleState.Initializing;
            
            // Clear previous battle data
            ClearBattleField();
            
            // Load player team
            LoadPlayerTeam();
            
            // Load enemy team based on level and stage
            LoadEnemyTeam(currentLevel, currentStage);
            
            // Setup battle UI
            battleUI?.InitializeBattle(playerTeam, enemyTeam);
            
            // Wait for battle start delay
            yield return new WaitForSeconds(battleStartDelay);
            
            // Start battle
            currentBattleState = BattleState.Battle;
            OnBattleLog?.Invoke($"Battle {currentLevel}-{currentStage} begins!");
            
            StartCoroutine(BattleLoop());
        }
        
        private void LoadPlayerTeam()
        {
            playerTeam.Clear();
            
            // Get player data from PlayerManager
            var playerManager = PlayerManager.Instance;
            if (playerManager == null) return;
            
            // Load player character
            var playerData = playerManager.GetPlayerCharacterData();
            if (playerData != null)
            {
                var playerCharacter = CreateBattleCharacter(playerData, true);
                playerTeam.Add(playerCharacter);
            }
            
            // Load team members
            var teamMembers = playerManager.GetTeamMembers();
            foreach (var memberData in teamMembers)
            {
                if (playerTeam.Count >= maxTeamSize) break;
                
                var teamMember = CreateBattleCharacter(memberData, true);
                playerTeam.Add(teamMember);
            }
            
            allCharacters.AddRange(playerTeam);
        }
        
        private void LoadEnemyTeam(int level, int stage)
        {
            enemyTeam.Clear();
            
            // Get enemy data from DataManager
            var dataManager = DataManager.Instance;
            if (dataManager == null) return;
            
            // Determine if this is a boss battle (every 10th stage)
            isBossBattle = stage % 10 == 0;
            
            // Load enemies based on level and stage
            var enemyDataList = dataManager.GetEnemiesForStage(level, stage);
            
            foreach (var enemyData in enemyDataList)
            {
                if (enemyTeam.Count >= maxMonstersPerBattle) break;
                
                var enemy = CreateBattleCharacter(enemyData, false);
                enemyTeam.Add(enemy);
            }
            
            allCharacters.AddRange(enemyTeam);
        }
        
        private BattleCharacter CreateBattleCharacter(CharacterData data, bool isPlayerTeam)
        {
            // Create battle character GameObject
            GameObject characterGO = new GameObject($"{data.name}_{data.id}");
            characterGO.transform.SetParent(isPlayerTeam ? playerTeamParent : enemyTeamParent);
            
            // Add BattleCharacter component
            var battleCharacter = characterGO.AddComponent<BattleCharacter>();
            battleCharacter.Initialize(data, isPlayerTeam);
            
            // Subscribe to events
            battleCharacter.OnDeath += HandleCharacterDeath;
            battleCharacter.OnActionReady += HandleCharacterActionReady;
            
            return battleCharacter;
        }
        
        private IEnumerator BattleLoop()
        {
            while (currentBattleState == BattleState.Battle)
            {
                // Update action bars for all characters
                UpdateActionBars();
                
                // Process ready actions
                ProcessActionQueue();
                
                // Check battle end conditions
                if (CheckBattleEndConditions())
                {
                    break;
                }
                
                yield return null;
            }
        }
        
        private void UpdateActionBars()
        {
            foreach (var character in allCharacters)
            {
                if (character.IsAlive)
                {
                    character.UpdateActionBar(actionBarSpeed * Time.deltaTime);
                }
            }
        }
        
        private void ProcessActionQueue()
        {
            while (actionQueue.Count > 0)
            {
                var character = actionQueue.Dequeue();
                if (character.IsAlive)
                {
                    StartCoroutine(ProcessCharacterAction(character));
                }
            }
        }
        
        private IEnumerator ProcessCharacterAction(BattleCharacter character)
        {
            currentActor = character;
            
            // Determine action based on AI or player input
            BattleAction action = null;
            
            if (character.IsPlayerTeam && !isAutoBattle && character == playerTeam[0])
            {
                // Wait for player input
                yield return StartCoroutine(WaitForPlayerInput(character));
                action = character.GetSelectedAction();
            }
            else
            {
                // Use AI to determine action
                action = character.GetAIAction(GetValidTargets(character));
            }
            
            if (action != null)
            {
                yield return StartCoroutine(ExecuteAction(character, action));
            }
            
            currentActor = null;
        }
        
        private IEnumerator WaitForPlayerInput(BattleCharacter character)
        {
            battleUI?.ShowActionSelection(character, GetValidTargets(character));
            
            while (!character.HasSelectedAction())
            {
                yield return null;
            }
            
            battleUI?.HideActionSelection();
        }
        
        private IEnumerator ExecuteAction(BattleCharacter actor, BattleAction action)
        {
            // Execute the action
            var results = actor.ExecuteAction(action);
            
            // Apply results to targets
            foreach (var result in results)
            {
                result.target.ApplyActionResult(result);
                
                // Log the action
                string logMessage = $"{actor.CharacterName} used {action.skill.name}";
                if (result.damage > 0)
                {
                    logMessage += $" dealing {result.damage} damage to {result.target.CharacterName}";
                }
                else if (result.healing > 0)
                {
                    logMessage += $" healing {result.healing} HP to {result.target.CharacterName}";
                }
                
                OnBattleLog?.Invoke(logMessage);
            }
            
            // Trigger skill used event
            OnSkillUsed?.Invoke(actor, action.skill, action.targets);
            
            // Wait for animation
            yield return new WaitForSeconds(action.skill.animationDuration);
            
            // Reset character's action bar
            actor.ResetActionBar();
        }
        
        private List<BattleCharacter> GetValidTargets(BattleCharacter actor)
        {
            List<BattleCharacter> validTargets = new List<BattleCharacter>();
            
            if (actor.IsPlayerTeam)
            {
                // Player can target enemies and allies
                validTargets.AddRange(enemyTeam.FindAll(c => c.IsAlive));
                validTargets.AddRange(playerTeam.FindAll(c => c.IsAlive));
            }
            else
            {
                // Enemies target player team
                validTargets.AddRange(playerTeam.FindAll(c => c.IsAlive));
            }
            
            return validTargets;
        }
        
        private void HandleCharacterActionReady(BattleCharacter character)
        {
            actionQueue.Enqueue(character);
        }
        
        private void HandleCharacterDeath(BattleCharacter character)
        {
            OnCharacterDeath?.Invoke(character);
            OnBattleLog?.Invoke($"{character.CharacterName} has been defeated!");
            
            // Remove from all characters list
            allCharacters.Remove(character);
        }
        
        private bool CheckBattleEndConditions()
        {
            bool playerTeamAlive = playerTeam.Exists(c => c.IsAlive);
            bool enemyTeamAlive = enemyTeam.Exists(c => c.IsAlive);
            
            if (!playerTeamAlive)
            {
                // Player team defeated
                StartCoroutine(EndBattle(false, false));
                return true;
            }
            else if (!enemyTeamAlive)
            {
                // Enemy team defeated
                StartCoroutine(EndBattle(true, isBossBattle));
                return true;
            }
            
            return false;
        }
        
        private IEnumerator EndBattle(bool victory, bool bossDefeated)
        {
            currentBattleState = BattleState.Ending;
            
            if (victory)
            {
                OnBattleLog?.Invoke("Victory!");
                
                // Process rewards
                ProcessBattleRewards();
                
                // Show victory UI
                battleUI?.ShowVictoryScreen(bossDefeated);
            }
            else
            {
                OnBattleLog?.Invoke("Defeat!");
                
                // Show defeat UI
                battleUI?.ShowDefeatScreen();
            }
            
            yield return new WaitForSeconds(2f);
            
            // Notify GameManager
            OnBattleComplete?.Invoke(victory, bossDefeated);
            GameManager.Instance?.OnBattleComplete(victory, bossDefeated);
        }
        
        private void ProcessBattleRewards()
        {
            var playerManager = PlayerManager.Instance;
            if (playerManager == null) return;
            
            int goldReward = 0;
            int expReward = 0;
            List<DropData> drops = new List<DropData>();
            
            // Calculate rewards from defeated enemies
            foreach (var enemy in enemyTeam)
            {
                goldReward += enemy.GetGoldReward();
                expReward += enemy.GetExpReward();
                drops.AddRange(enemy.GetDrops());
            }
            
            // Apply rewards
            playerManager.AddGold(goldReward);
            playerManager.AddExperience(expReward);
            
            foreach (var drop in drops)
            {
                playerManager.AddItem(drop);
            }
            
            OnBattleLog?.Invoke($"Gained {goldReward} gold and {expReward} experience!");
        }
        
        private void ClearBattleField()
        {
            // Clear previous characters
            foreach (var character in allCharacters)
            {
                if (character != null)
                {
                    Destroy(character.gameObject);
                }
            }
            
            playerTeam.Clear();
            enemyTeam.Clear();
            allCharacters.Clear();
            actionQueue.Clear();
        }
        
        public void SetAutoBattle(bool auto)
        {
            isAutoBattle = auto;
            OnBattleLog?.Invoke($"Auto battle: {(auto ? "ON" : "OFF")}");
        }
        
        public void TryCatchMonster(BattleCharacter monster)
        {
            if (monster.IsPlayerTeam || !monster.IsAlive) return;
            
            float catchChance = monster.GetCatchProbability();
            float roll = Random.Range(0f, 1f);
            
            if (roll <= catchChance)
            {
                // Successful catch
                PlayerManager.Instance?.AddCaughtMonster(monster.GetCharacterData());
                OnBattleLog?.Invoke($"Successfully caught {monster.CharacterName}!");
                
                // Remove monster from battle
                monster.Die();
            }
            else
            {
                OnBattleLog?.Invoke($"Failed to catch {monster.CharacterName}!");
            }
        }
    }
    
    public enum BattleState
    {
        Initializing,
        Battle,
        Ending
    }
}
