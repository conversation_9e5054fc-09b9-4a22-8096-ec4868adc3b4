Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.50f1 (f1ef1dca8bff) revision 15855389'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Enterprise' Language: 'en' Physical Memory: 32557 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1
Date: 2025-07-30T12:34:46Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.50f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Workspace/Unity/PetingGame
-logFile
Logs/AssetImportWorker2.log
-srvPort
54153
-job-worker-count
13
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: C:/Workspace/Unity/PetingGame
C:/Workspace/Unity/PetingGame
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [20620]  Target information:

Player connection [20620]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1993506087 [EditorId] 1993506087 [Version] 1048832 [Id] WindowsEditor(7,Raymond) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [20620]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 1993506087 [EditorId] 1993506087 [Version] 1048832 [Id] WindowsEditor(7,Raymond) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Unable to join player connection multicast group (err: 10013).
Player connection [20620] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 13
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 1.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.50f1 (f1ef1dca8bff)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Workspace/Unity/PetingGame/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 5070 (ID=0x2f04)
    Vendor:   NVIDIA
    VRAM:     11855 MB
    Driver:   32.0.15.7652
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56572
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.50f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.001944 seconds.
- Loaded All Assemblies, in  0.239 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 244 ms
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.458 seconds
Domain Reload Profiling: 696ms
	BeginReloadAssembly (79ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (98ms)
		LoadAssemblies (78ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (96ms)
			TypeCache.Refresh (95ms)
				TypeCache.ScanAssembly (87ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (458ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (430ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (297ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (32ms)
			ProcessInitializeOnLoadAttributes (68ms)
			ProcessInitializeOnLoadMethodAttributes (30ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.493 seconds
Refreshing native plugins compatible for Editor in 1.03 ms, found 4 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.464 seconds
Domain Reload Profiling: 955ms
	BeginReloadAssembly (107ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (18ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (332ms)
		LoadAssemblies (223ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (168ms)
			TypeCache.Refresh (124ms)
				TypeCache.ScanAssembly (112ms)
			BuildScriptInfoCaches (36ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (464ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (374ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (2ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (220ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (3ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.51 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 213 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6593 unused Assets / (6.7 MB). Loaded Objects now: 7254.
Memory consumption went from 183.6 MB to 176.9 MB.
Total: 9.303200 ms (FindLiveObjects: 0.579700 ms CreateObjectMapping: 0.518300 ms MarkObjects: 4.840400 ms  DeleteObjects: 3.363800 ms)

========================================================================
Received Import Request.
  Time since last request: 82745.314477 seconds.
  path: Assets/Settings/Lit2DSceneTemplate.scenetemplate
  artifactKey: Guid(d03ed43fc9d8a4f2e9fa70c1c7916eb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Settings/Lit2DSceneTemplate.scenetemplate using Guid(d03ed43fc9d8a4f2e9fa70c1c7916eb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '05916363aecc207023dda6006c825dde') in 0.0674267 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 95.056864 seconds.
  path: Assets
  artifactKey: Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets using Guid(00000000000000001000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c391582d739b6e62a5220f35cbb167ac') in 0.0006823 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.502 seconds
Refreshing native plugins compatible for Editor in 0.78 ms, found 4 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Default port 6400 is in use, searching for alternative...
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:FindAvailablePort () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:73)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:43)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs Line: 73)

Found available port 6401
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:FindAvailablePort () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:80)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:43)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs Line: 80)

Saved port 6401 to storage
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:SavePort (int) (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:130)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:44)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs Line: 130)

UnityMcpBridge started on port 6401.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:115)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs Line: 115)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.239 seconds
Domain Reload Profiling: 1741ms
	BeginReloadAssembly (140ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (16ms)
	LoadAllAssembliesAndSetupDomain (313ms)
		LoadAssemblies (212ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (160ms)
			TypeCache.Refresh (83ms)
				TypeCache.ScanAssembly (73ms)
			BuildScriptInfoCaches (68ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1239ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1144ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (1022ms)
			ProcessInitializeOnLoadMethodAttributes (35ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 1.82 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 36 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6622 unused Assets / (7.1 MB). Loaded Objects now: 7291.
Memory consumption went from 159.1 MB to 152.0 MB.
Total: 9.906100 ms (FindLiveObjects: 0.524300 ms CreateObjectMapping: 0.478500 ms MarkObjects: 5.360500 ms  DeleteObjects: 3.539200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1035.045098 seconds.
  path: Assets/Script
  artifactKey: Guid(188024fb5e3128d4ea9da1eb61cfcdbc) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script using Guid(188024fb5e3128d4ea9da1eb61cfcdbc) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: 'de4e8437a5a0c2744047fc77489d59de') in 0.1564508 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.052323 seconds.
  path: Assets/Script
  artifactKey: Guid(188024fb5e3128d4ea9da1eb61cfcdbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script using Guid(188024fb5e3128d4ea9da1eb61cfcdbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ed8bd9bf397b31629d1effc34454b1a3') in 0.00051 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.453493 seconds.
  path: Assets/Script/README.md
  artifactKey: Guid(72cf78f392b1cc7469a1107b5aab743e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/README.md using Guid(72cf78f392b1cc7469a1107b5aab743e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '53c690931610143a69c0db2dc6f8ec8d') in 0.0184292 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 1.53 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 36 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6612 unused Assets / (7.2 MB). Loaded Objects now: 7292.
Memory consumption went from 159.3 MB to 152.2 MB.
Total: 12.049800 ms (FindLiveObjects: 0.552500 ms CreateObjectMapping: 0.475800 ms MarkObjects: 7.158300 ms  DeleteObjects: 3.861700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 36.893699 seconds.
  path: Assets/Script/Test.cs
  artifactKey: Guid(66e0f952d8e2746428d87f25108835a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/Test.cs using Guid(66e0f952d8e2746428d87f25108835a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4e66d41e0472e1b6b64feca7ef7cd580') in 0.0028964 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.245987 seconds.
  path: Assets/Script/Test.cs
  artifactKey: Guid(66e0f952d8e2746428d87f25108835a3) Importer(2089858483,192608db42251d675da83286b1cce702)
Start importing Assets/Script/Test.cs using Guid(66e0f952d8e2746428d87f25108835a3) Importer(2089858483,192608db42251d675da83286b1cce702) (ScriptedImporter) -> (artifact id: '0814b8fff2254fcd21afc1a3d3b6b8fe') in 0.0546462 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.791 seconds
Refreshing native plugins compatible for Editor in 1.75 ms, found 4 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Default port 6400 is in use, searching for alternative...
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:FindAvailablePort () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:73)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:43)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs Line: 73)

Found available port 6401
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:FindAvailablePort () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:80)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:43)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs Line: 80)

Saved port 6401 to storage
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.Helpers.PortManager:SavePort (int) (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:130)
UnityMcpBridge.Editor.Helpers.PortManager:GetPortWithFallback () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs:44)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:109)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/Helpers/PortManager.cs Line: 130)

UnityMcpBridge started on port 6401.
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
UnityMcpBridge.Editor.UnityMcpBridge:Start () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:115)
UnityMcpBridge.Editor.UnityMcpBridge:.cctor () (at ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs:84)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.justinpbarnett.unity-mcp@cfc7497f08fa/Editor/UnityMcpBridge.cs Line: 115)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.732 seconds
Domain Reload Profiling: 2522ms
	BeginReloadAssembly (230ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (496ms)
		LoadAssemblies (343ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (241ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (208ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1733ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1549ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (15ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (138ms)
			ProcessInitializeOnLoadAttributes (1315ms)
			ProcessInitializeOnLoadMethodAttributes (72ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 2.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 36 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6623 unused Assets / (6.8 MB). Loaded Objects now: 7294.
Memory consumption went from 159.2 MB to 152.4 MB.
Total: 12.942900 ms (FindLiveObjects: 0.902400 ms CreateObjectMapping: 1.035200 ms MarkObjects: 6.791700 ms  DeleteObjects: 4.212300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 8.371626 seconds.
  path: Assets/Script/Test.cs
  artifactKey: Guid(66e0f952d8e2746428d87f25108835a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Script/Test.cs using Guid(66e0f952d8e2746428d87f25108835a3) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '2569071661550fad84686fdd9064e506') in 0.0048496 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

