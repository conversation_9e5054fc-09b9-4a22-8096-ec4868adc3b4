using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

namespace PetingGame.UI
{
    /// <summary>
    /// 管理UI在不同解析度設備上的自適應顯示
    /// </summary>
    public class AdaptiveUIManager : MonoBehaviour
    {
        [Header("UI Scaling Settings")]
        [SerializeField] private CanvasScaler.ScaleMode scaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        [SerializeField] private Vector2 referenceResolution = new Vector2(320, 240);
        [SerializeField] private float matchWidthOrHeight = 0.5f;
        
        [Header("Safe Area Settings")]
        [SerializeField] private bool useSafeArea = true;
        [SerializeField] private bool adaptToNotch = true;
        
        [Header("Touch Area Settings")]
        [SerializeField] private float minTouchAreaSize = 44f; // iOS HIG recommendation
        [SerializeField] private bool autoAdjustTouchAreas = true;
        
        private Canvas mainCanvas;
        private CanvasScaler canvasScaler;
        private RectTransform canvasRectTransform;
        private List<AdaptiveUIElement> adaptiveElements = new List<AdaptiveUIElement>();
        
        // Safe area properties
        private Rect lastSafeArea = new Rect(0, 0, 0, 0);
        private RectTransform safeAreaTransform;
        
        private void Awake()
        {
            SetupCanvas();
            SetupSafeArea();
            RegisterForResolutionChanges();
        }
        
        private void Start()
        {
            ApplyAdaptiveSettings();
            RefreshSafeArea();
        }
        
        private void SetupCanvas()
        {
            mainCanvas = GetComponent<Canvas>();
            if (mainCanvas == null)
            {
                mainCanvas = gameObject.AddComponent<Canvas>();
            }
            
            canvasScaler = GetComponent<CanvasScaler>();
            if (canvasScaler == null)
            {
                canvasScaler = gameObject.AddComponent<CanvasScaler>();
            }
            
            canvasRectTransform = GetComponent<RectTransform>();
            
            // 設置Canvas為Screen Space - Overlay
            mainCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
            mainCanvas.sortingOrder = 100; // 確保UI在最上層
        }
        
        private void SetupSafeArea()
        {
            if (!useSafeArea) return;
            
            // 創建安全區域容器
            GameObject safeAreaObj = new GameObject("SafeArea");
            safeAreaObj.transform.SetParent(transform, false);
            
            safeAreaTransform = safeAreaObj.AddComponent<RectTransform>();
            safeAreaTransform.anchorMin = Vector2.zero;
            safeAreaTransform.anchorMax = Vector2.one;
            safeAreaTransform.offsetMin = Vector2.zero;
            safeAreaTransform.offsetMax = Vector2.zero;
            
            // 將現有的UI元素移動到安全區域內
            List<Transform> children = new List<Transform>();
            for (int i = 0; i < transform.childCount; i++)
            {
                Transform child = transform.GetChild(i);
                if (child != safeAreaTransform)
                {
                    children.Add(child);
                }
            }
            
            foreach (Transform child in children)
            {
                child.SetParent(safeAreaTransform, true);
            }
        }
        
        private void RegisterForResolutionChanges()
        {
            if (PixelPerfectManager.Instance != null)
            {
                PixelPerfectManager.Instance.OnResolutionChanged += OnResolutionChanged;
                PixelPerfectManager.Instance.OnScaleFactorChanged += OnScaleFactorChanged;
            }
        }
        
        private void ApplyAdaptiveSettings()
        {
            // 設置CanvasScaler
            canvasScaler.uiScaleMode = scaleMode;
            canvasScaler.referenceResolution = referenceResolution;
            canvasScaler.matchWidthOrHeight = matchWidthOrHeight;
            
            // 根據像素完美管理器調整設置
            if (PixelPerfectManager.Instance != null)
            {
                Vector2 baseResolution = PixelPerfectManager.Instance.GetBaseResolution();
                canvasScaler.referenceResolution = baseResolution;
            }
            
            // 調整觸控區域
            if (autoAdjustTouchAreas)
            {
                AdjustTouchAreas();
            }
        }
        
        private void RefreshSafeArea()
        {
            if (!useSafeArea || safeAreaTransform == null) return;
            
            Rect safeArea = Screen.safeArea;
            
            // 檢查是否需要更新
            if (safeArea == lastSafeArea) return;
            
            lastSafeArea = safeArea;
            
            // 轉換安全區域到Canvas座標
            Vector2 anchorMin = safeArea.position;
            Vector2 anchorMax = safeArea.position + safeArea.size;
            
            anchorMin.x /= Screen.width;
            anchorMin.y /= Screen.height;
            anchorMax.x /= Screen.width;
            anchorMax.y /= Screen.height;
            
            // 考慮像素完美管理器的viewport
            if (PixelPerfectManager.Instance != null)
            {
                Rect viewport = PixelPerfectManager.Instance.GetSafeArea();
                
                // 調整安全區域以適應viewport
                anchorMin.x = Mathf.Max(anchorMin.x, viewport.x);
                anchorMin.y = Mathf.Max(anchorMin.y, viewport.y);
                anchorMax.x = Mathf.Min(anchorMax.x, viewport.x + viewport.width);
                anchorMax.y = Mathf.Min(anchorMax.y, viewport.y + viewport.height);
            }
            
            safeAreaTransform.anchorMin = anchorMin;
            safeAreaTransform.anchorMax = anchorMax;
            safeAreaTransform.offsetMin = Vector2.zero;
            safeAreaTransform.offsetMax = Vector2.zero;
            
            Debug.Log($"Safe Area Updated: {anchorMin} to {anchorMax}");
        }
        
        private void AdjustTouchAreas()
        {
            // 查找所有可觸控的UI元素
            Button[] buttons = FindObjectsOfType<Button>();
            
            foreach (Button button in buttons)
            {
                AdjustButtonTouchArea(button);
            }
        }
        
        private void AdjustButtonTouchArea(Button button)
        {
            RectTransform buttonRect = button.GetComponent<RectTransform>();
            if (buttonRect == null) return;
            
            // 計算當前按鈕的實際像素大小
            Vector3[] corners = new Vector3[4];
            buttonRect.GetWorldCorners(corners);
            
            float width = Vector3.Distance(corners[0], corners[3]);
            float height = Vector3.Distance(corners[0], corners[1]);
            
            // 檢查是否需要調整
            bool needsAdjustment = width < minTouchAreaSize || height < minTouchAreaSize;
            
            if (needsAdjustment)
            {
                // 添加或調整觸控區域擴展組件
                TouchAreaExpander expander = button.GetComponent<TouchAreaExpander>();
                if (expander == null)
                {
                    expander = button.gameObject.AddComponent<TouchAreaExpander>();
                }
                
                expander.SetMinimumSize(minTouchAreaSize);
            }
        }
        
        private void OnResolutionChanged(Vector2 newResolution)
        {
            // 解析度改變時重新應用設置
            ApplyAdaptiveSettings();
            RefreshSafeArea();
            
            // 通知所有自適應元素
            foreach (var element in adaptiveElements)
            {
                if (element != null)
                {
                    element.OnResolutionChanged(newResolution);
                }
            }
        }
        
        private void OnScaleFactorChanged(int newScaleFactor)
        {
            // 縮放因子改變時調整UI
            foreach (var element in adaptiveElements)
            {
                if (element != null)
                {
                    element.OnScaleFactorChanged(newScaleFactor);
                }
            }
        }
        
        public void RegisterAdaptiveElement(AdaptiveUIElement element)
        {
            if (!adaptiveElements.Contains(element))
            {
                adaptiveElements.Add(element);
            }
        }
        
        public void UnregisterAdaptiveElement(AdaptiveUIElement element)
        {
            adaptiveElements.Remove(element);
        }
        
        public Vector2 GetSafeAreaSize()
        {
            if (safeAreaTransform != null)
            {
                return safeAreaTransform.rect.size;
            }
            return canvasRectTransform.rect.size;
        }
        
        public RectTransform GetSafeAreaTransform()
        {
            return safeAreaTransform ?? canvasRectTransform;
        }
        
        private void Update()
        {
            // 檢查安全區域變化（用於設備旋轉等情況）
            if (useSafeArea && Screen.safeArea != lastSafeArea)
            {
                RefreshSafeArea();
            }
        }
        
        private void OnDestroy()
        {
            if (PixelPerfectManager.Instance != null)
            {
                PixelPerfectManager.Instance.OnResolutionChanged -= OnResolutionChanged;
                PixelPerfectManager.Instance.OnScaleFactorChanged -= OnScaleFactorChanged;
            }
        }
    }
    
    /// <summary>
    /// 自適應UI元素基類
    /// </summary>
    public abstract class AdaptiveUIElement : MonoBehaviour
    {
        public abstract void OnResolutionChanged(Vector2 newResolution);
        public abstract void OnScaleFactorChanged(int newScaleFactor);
        
        protected virtual void Start()
        {
            AdaptiveUIManager uiManager = FindObjectOfType<AdaptiveUIManager>();
            if (uiManager != null)
            {
                uiManager.RegisterAdaptiveElement(this);
            }
        }
        
        protected virtual void OnDestroy()
        {
            AdaptiveUIManager uiManager = FindObjectOfType<AdaptiveUIManager>();
            if (uiManager != null)
            {
                uiManager.UnregisterAdaptiveElement(this);
            }
        }
    }
    
    /// <summary>
    /// 觸控區域擴展組件
    /// </summary>
    public class TouchAreaExpander : MonoBehaviour, ICanvasRaycastFilter
    {
        private float minimumSize = 44f;
        private RectTransform rectTransform;
        
        private void Awake()
        {
            rectTransform = GetComponent<RectTransform>();
        }
        
        public void SetMinimumSize(float size)
        {
            minimumSize = size;
        }
        
        public bool IsRaycastLocationValid(Vector2 screenPoint, Camera eventCamera)
        {
            // 擴展觸控區域
            Vector2 localPoint;
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                rectTransform, screenPoint, eventCamera, out localPoint);
            
            Rect rect = rectTransform.rect;
            
            // 計算擴展區域
            float expandX = Mathf.Max(0, (minimumSize - rect.width) / 2f);
            float expandY = Mathf.Max(0, (minimumSize - rect.height) / 2f);
            
            Rect expandedRect = new Rect(
                rect.x - expandX,
                rect.y - expandY,
                rect.width + expandX * 2,
                rect.height + expandY * 2
            );
            
            return expandedRect.Contains(localPoint);
        }
    }
}
