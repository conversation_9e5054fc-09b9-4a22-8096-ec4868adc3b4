{"name": "Unity.RenderPipelines.Core.Samples.Editor", "rootNamespace": "", "references": ["Unity.RenderPipelines.Core.Runtime", "Unity.RenderPipelines.Core.Editor", "Unity.RenderPipelines.Core.Samples.Runtime", "Unity.TextMeshPro", "Unity.InputSystem"], "includePlatforms": ["Editor"], "excludePlatforms": [], "allowUnsafeCode": false, "overrideReferences": true, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [{"name": "com.unity.inputsystem", "expression": "1.0.0", "define": "INPUT_SYSTEM_INSTALLED"}], "noEngineReferences": false}