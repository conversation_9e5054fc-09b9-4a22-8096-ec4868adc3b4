//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef OCCLUSIONCULLINGCOMMON_CS_HLSL
#define OCCLUSIONCULLINGCOMMON_CS_HLSL
//
// UnityEngine.Rendering.OcclusionCullingCommonConfig:  static fields
//
#define OCCLUSIONCULLINGCOMMONCONFIG_MAX_OCCLUDER_MIPS (8)
#define OCCLUSIONCULLINGCOMMONCONFIG_MAX_OCCLUDER_SILHOUETTE_PLANES (6)
#define OCCLUSIONCULLINGCOMMONCONFIG_MAX_SUBVIEWS_PER_VIEW (6)
#define OCCLUSIONCULLINGCOMMONCONFIG_DEBUG_PYRAMID_OFFSET (4)

//
// UnityEngine.Rendering.OcclusionTestDebugFlag:  static fields
//
#define OCCLUSIONTESTDEBUGFLAG_ALWAYS_PASS (1)
#define OCCLUSIONTESTDEBUGFLAG_COUNT_VISIBLE (2)


#endif
