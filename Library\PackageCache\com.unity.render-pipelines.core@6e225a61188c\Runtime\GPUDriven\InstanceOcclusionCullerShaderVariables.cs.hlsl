//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef INSTANCEOCCLUSIONCULLERSHADERVARIABLES_CS_HLSL
#define INSTANCEOCCLUSIONCULLERSHADERVARIABLES_CS_HLSL
// Generated from UnityEngine.Rendering.InstanceOcclusionCullerShaderVariables
// PackingRules = Exact
CBUFFER_START(InstanceOcclusionCullerShaderVariables)
    uint _DrawInfoAllocIndex;
    uint _DrawInfoCount;
    uint _InstanceInfoAllocIndex;
    uint _InstanceInfoCount;
    int _BoundingSphereInstanceDataAddress;
    int _DebugCounterIndex;
    int _InstanceMultiplierShift;
    int _InstanceOcclusionCullerPad0;
CBUFFER_END


#endif
