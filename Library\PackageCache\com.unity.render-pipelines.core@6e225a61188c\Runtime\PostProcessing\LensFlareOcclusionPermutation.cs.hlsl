//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef LENSFLAREOCCLUSIONPERMUTATION_CS_HLSL
#define LENSFLAREOCCLUSIONPERMUTATION_CS_HLSL
//
// UnityEngine.Rendering.LensFlareOcclusionPermutation:  static fields
//
#define LENSFLAREOCCLUSIONPERMUTATION_DEPTH (1)
#define LENSFLAREOCCLUSIONPERMUTATION_FOG_OPACITY (4)


#endif
