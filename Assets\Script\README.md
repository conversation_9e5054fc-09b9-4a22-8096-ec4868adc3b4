I want to create to console like battle game in unity. which the screen divided into 3 part. the top part the the battle part, battle log and the bottom part is the user control part.

the battle screen is pixel top down view. user can choose to control or auto the battle with the monster.

player will automatically enter the battle at time start and when the battle is over, the player will be automatically enter the next battle.
whenever player battle with the boss of the level, the player will be automatically enter the next level.

all the configuration in game should be in csv file. the monster, equipment, skill, etc.

rarity:
all the monster and equipment has the rarity. including common, rare, epic, legendary, mythic. the higher the rarity, the higher the attribute.


basic attribute:
all the monster and player has the basic attribute: hp, attack, defense, speed, critical rate, critical damage.
all the monster and player has the basic skill: attack, defend.
all the monster and player action is based on the speed. the faster the monster, the faster the action.
all the monster and player has the equipment. including weapon (add attack), armor (add defense), accessory (critical rate, critical damage), helmet (add hp), shoes (add speed).


equipment:
the equipment has the level. the higher the level, the higher the attribute.
the equipment has passive skill.
the equipment has the rarity. the higher the rarity, more attribute and passive skill.
Passsive skill amount limit:
common:1
rare:2
epic:3
legendary:4
mythic:5
the monster has the drop rate. the higher the level, the higher level equipment will drop.

skill:
in game there are passive and active skill
the higher rarity monster has more active, passive skill and attribute.
Active skill amount limit:
common:1
rare:2
epic:3
legendary:4
mythic:5
player:5
active skill has cooldown.
passive skill also has cooldown.
passive amount is unlimited.
some of the skill will assign some status 

battle:
each monster and play has the action progress (not show in ui), more speed the action bar will run faster, then the moster or play will perform faster.
monster will have a simple ai to choose the target and skill to use. which will target the player with will due most damage (base on the reminding hp and the defense)
in a battle, will have most 10 monster.
player can at most group with other 4 team member.
player can choose to auto battle or manual battle.
in manual battle, player can choose the target and skill to use.
in auto battle, the simple ai which same as monster ai will be used.
each monster will have probability to drop the equipment, active skill, gold. player need to win the battle to get the reward.
the battle will end when all the monster is dead or the player is dead.
player has a skill to catch the monster when the monster is low hp. when the player catch the monster, the monster will be added to the player team, player can choose the team member in the team page at the menu.
the caught monster will be start at level 1.

system:
the game have the main menu which contains battle (back to battle), equipment, skill, team,setting, lucky draw.
when user enter the main menu, the battle will still continue on the top.
the equipment and skill page will show the player equipment and skill. player can change the equipment and skill.
the team page will show the player team member. player can change the team member.
the setting page will show the player setting. player can change the setting.
the lucky draw page will show the player lucky draw, user can use the gold to draw the reward. including:
1. passive skill pool
2. equipment pool
3. active skill pool

