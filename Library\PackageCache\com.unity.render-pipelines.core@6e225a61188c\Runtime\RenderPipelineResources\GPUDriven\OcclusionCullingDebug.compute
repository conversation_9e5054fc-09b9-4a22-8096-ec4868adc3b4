#pragma kernel ClearOcclusionDebug

#include "Packages/com.unity.render-pipelines.core/ShaderLibrary/Common.hlsl"

#define OCCLUSION_DEBUG 1

#include "Packages/com.unity.render-pipelines.core/Runtime/GPUDriven/OcclusionCullingCommon.hlsl"

[numthreads(8, 8, 1)]
void ClearOcclusionDebug(uint2 dispatchIndex : SV_DispatchThreadID, uint3 groupId : SV_GroupID)
{
#ifdef OCCLUSION_DEBUG
    int clearSliceIndex = groupId.z;

    for (int i = 0; i < OCCLUSIONCULLINGCOMMONCONFIG_MAX_OCCLUDER_MIPS; ++i)
    {
        uint4 bounds = _OccluderMipBounds[i];
        bounds.y += clearSliceIndex * _OccluderMipLayoutSizeY;
        if (all(dispatchIndex < bounds.zw))
            _OcclusionDebugOverlay[OcclusionDebugOverlayOffset(bounds.xy + dispatchIndex)] = 0;
    }

    if (all(dispatchIndex == 0) && clearSliceIndex == 0)
        _OcclusionDebugOverlay[0] = 0;
#endif
}
