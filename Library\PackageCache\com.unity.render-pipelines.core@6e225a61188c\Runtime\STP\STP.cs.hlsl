//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef STP_CS_HLSL
#define STP_CS_HLSL
//
// UnityEngine.Rendering.STP+StpSetupPerViewConstants:  static fields
//
#define STPSETUPPERVIEWCONSTANTS_COUNT (8)

// Generated from UnityEngine.Rendering.STP+StpConstantBufferData
// PackingRules = Exact
CBUFFER_START(StpConstantBufferData)
    float4 _StpCommonConstant;
    float4 _StpSetupConstants0;
    float4 _StpSetupConstants1;
    float4 _StpSetupConstants2;
    float4 _StpSetupConstants3;
    float4 _StpSetupConstants4;
    float4 _StpSetupConstants5;
    float4 _StpSetupPerViewConstants[16];
    float4 _StpDilConstants0;
    float4 _StpTaaConstants0;
    float4 _StpTaaConstants1;
    float4 _StpTaaConstants2;
    float4 _StpTaaConstants3;
CBUFFER_END


#endif
