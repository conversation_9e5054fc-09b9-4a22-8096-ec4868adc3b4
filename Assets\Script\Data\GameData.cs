using System;
using System.Collections.Generic;
using UnityEngine;

namespace PetingGame.Data
{
    /// <summary>
    /// Base class for all game data structures
    /// </summary>
    [Serializable]
    public abstract class BaseData
    {
        public int id;
        public string name;
        public string description;
        public Rarity rarity;
    }
    
    /// <summary>
    /// Character/Monster data structure
    /// </summary>
    [Serializable]
    public class CharacterData : BaseData
    {
        [Header("Basic Attributes")]
        public int baseHp;
        public int baseAttack;
        public int baseDefense;
        public int baseSpeed;
        public float baseCriticalRate;
        public float baseCriticalDamage;
        
        [Header("Skills")]
        public List<int> activeSkillIds = new List<int>();
        public List<int> passiveSkillIds = new List<int>();
        
        [Header("Equipment Slots")]
        public int weaponSlot = -1;
        public int armorSlot = -1;
        public int accessorySlot = -1;
        public int helmetSlot = -1;
        public int shoesSlot = -1;
        
        [Header("Monster Specific")]
        public bool isMonster;
        public int level = 1;
        public List<DropData> dropTable = new List<DropData>();
        public bool isBoss;
        public float catchProbability = 0.1f;
        
        [Header("AI Settings")]
        public AIType aiType = AIType.Aggressive;
        public float aiDecisionDelay = 1f;
    }
    
    /// <summary>
    /// Equipment data structure
    /// </summary>
    [Serializable]
    public class EquipmentData : BaseData
    {
        [Header("Equipment Properties")]
        public EquipmentType equipmentType;
        public int level = 1;
        public int maxLevel = 10;
        
        [Header("Stat Bonuses")]
        public int hpBonus;
        public int attackBonus;
        public int defenseBonus;
        public int speedBonus;
        public float criticalRateBonus;
        public float criticalDamageBonus;
        
        [Header("Passive Skills")]
        public List<int> passiveSkillIds = new List<int>();
        
        [Header("Upgrade Requirements")]
        public int upgradeCost;
        public List<int> upgradeRequiredItems = new List<int>();
    }
    
    /// <summary>
    /// Skill data structure
    /// </summary>
    [Serializable]
    public class SkillData : BaseData
    {
        [Header("Skill Properties")]
        public SkillType skillType;
        public TargetType targetType;
        public int cooldown;
        public int manaCost;
        
        [Header("Damage/Healing")]
        public float damageMultiplier = 1f;
        public int flatDamage;
        public int healAmount;
        
        [Header("Status Effects")]
        public List<StatusEffectData> statusEffects = new List<StatusEffectData>();
        
        [Header("Special Effects")]
        public bool ignoreDefense;
        public bool canCrit = true;
        public float criticalChanceBonus;
        public float criticalDamageBonus;
        
        [Header("Animation")]
        public string animationTrigger;
        public float animationDuration = 1f;
    }
    
    /// <summary>
    /// Status effect data structure
    /// </summary>
    [Serializable]
    public class StatusEffectData : BaseData
    {
        [Header("Effect Properties")]
        public StatusEffectType effectType;
        public int duration;
        public bool isStackable;
        public int maxStacks = 1;
        
        [Header("Effect Values")]
        public float value;
        public float percentage;
        public int tickDamage;
        public int tickHealing;
        
        [Header("Stat Modifiers")]
        public StatModifier[] statModifiers;
    }
    
    /// <summary>
    /// Drop data for monster rewards
    /// </summary>
    [Serializable]
    public class DropData
    {
        public DropType dropType;
        public int itemId;
        public int quantity = 1;
        public float dropChance = 0.1f;
        public int minLevel = 1;
        public int maxLevel = 1;
    }
    
    /// <summary>
    /// Stat modifier for status effects
    /// </summary>
    [Serializable]
    public class StatModifier
    {
        public StatType statType;
        public ModifierType modifierType;
        public float value;
    }
    
    /// <summary>
    /// Player save data
    /// </summary>
    [Serializable]
    public class PlayerSaveData
    {
        [Header("Progress")]
        public int currentLevel = 1;
        public int currentStage = 1;
        public int playerLevel = 1;
        public int experience;
        public int gold;
        
        [Header("Inventory")]
        public List<InventoryItem> inventory = new List<InventoryItem>();
        public List<int> ownedSkills = new List<int>();
        
        [Header("Team")]
        public List<int> teamMemberIds = new List<int>();
        public List<CharacterSaveData> ownedCharacters = new List<CharacterSaveData>();
        
        [Header("Equipment")]
        public List<EquipmentSaveData> ownedEquipment = new List<EquipmentSaveData>();
        
        [Header("Settings")]
        public bool autoBattle = true;
        public float masterVolume = 1f;
        public float sfxVolume = 1f;
        public float musicVolume = 1f;
    }
    
    /// <summary>
    /// Character save data
    /// </summary>
    [Serializable]
    public class CharacterSaveData
    {
        public int characterId;
        public int level = 1;
        public int experience;
        public List<int> equippedItems = new List<int>();
        public List<int> learnedSkills = new List<int>();
    }
    
    /// <summary>
    /// Equipment save data
    /// </summary>
    [Serializable]
    public class EquipmentSaveData
    {
        public int equipmentId;
        public int level = 1;
        public int experience;
        public List<int> enhancedPassiveSkills = new List<int>();
    }
    
    /// <summary>
    /// Inventory item
    /// </summary>
    [Serializable]
    public class InventoryItem
    {
        public int itemId;
        public int quantity;
        public ItemType itemType;
    }
    
    // Enums
    public enum Rarity
    {
        Common = 0,
        Rare = 1,
        Epic = 2,
        Legendary = 3,
        Mythic = 4
    }
    
    public enum EquipmentType
    {
        Weapon,
        Armor,
        Accessory,
        Helmet,
        Shoes
    }
    
    public enum SkillType
    {
        Active,
        Passive
    }
    
    public enum TargetType
    {
        Self,
        SingleEnemy,
        AllEnemies,
        SingleAlly,
        AllAllies,
        Random
    }
    
    public enum StatusEffectType
    {
        Damage,
        Heal,
        Buff,
        Debuff,
        Stun,
        Poison,
        Burn,
        Freeze,
        Shield
    }
    
    public enum DropType
    {
        Equipment,
        Skill,
        Gold,
        Experience,
        Item
    }
    
    public enum StatType
    {
        HP,
        Attack,
        Defense,
        Speed,
        CriticalRate,
        CriticalDamage
    }
    
    public enum ModifierType
    {
        Flat,
        Percentage
    }
    
    public enum AIType
    {
        Aggressive,
        Defensive,
        Balanced,
        Support
    }
    
    public enum ItemType
    {
        Equipment,
        Skill,
        Consumable,
        Material
    }
}
