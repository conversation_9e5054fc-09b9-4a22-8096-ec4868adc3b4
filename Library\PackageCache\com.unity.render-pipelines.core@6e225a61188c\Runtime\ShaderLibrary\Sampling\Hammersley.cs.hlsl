//
// This file was automatically generated. Please don't edit by hand. Execute Editor command [ Edit > Rendering > Generate Shader Includes ] instead
//

#ifndef HAMMERSLEY_CS_HLSL
#define HAMMERSLEY_CS_HLSL
// Generated from UnityEngine.Rendering.Hammersley+Hammersley2dSeq16
// PackingRules = Exact
CBUFFER_START(Hammersley2dSeq16)
    float4 hammersley2dSeq16[16];
CBUFFER_END

// Generated from UnityEngine.Rendering.Hammersley+Hammersley2dSeq256
// PackingRules = Exact
CBUFFER_START(Hammersley2dSeq256)
    float4 hammersley2dSeq256[256];
CBUFFER_END

// Generated from UnityEngine.Rendering.<PERSON><PERSON>+<PERSON>sley2dSeq32
// PackingRules = Exact
CBUFFER_START(Hammersley2dSeq32)
    float4 hammersley2dSeq32[32];
CBUFFER_END

// Generated from UnityEngine.Rendering.<PERSON><PERSON>+<PERSON>sley2dSeq64
// PackingRules = Exact
CBUFFER_START(<PERSON>sley2dSeq64)
    float4 hammersley2dSeq64[64];
CBUFFER_END


#endif
