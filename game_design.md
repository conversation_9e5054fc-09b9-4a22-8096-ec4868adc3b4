# 🎮 PetingGame 完整遊戲設計文檔

## 目錄
1. [遊戲概述](#1-遊戲概述)
2. [核心遊戲循環](#2-核心遊戲循環)
3. [Console風格UI設計](#3-console風格ui設計)
4. [怪物生成系統](#4-怪物生成系統)
5. [配置系統設計](#5-配置系統設計)
6. [標籤系統設計](#6-標籤系統設計)
7. [像素繪製特效系統](#7-像素繪製特效系統)
8. [技術架構](#8-技術架構)
9. [實作優先級](#9-實作優先級)

---

## 1. 遊戲概述

### 1.1 遊戲類型與視覺風格
**PetingGame** 是一款採用經典Console RPG風格的像素戰鬥收集遊戲，靈感來自於早期寶可夢等經典掌機遊戲。

### 1.2 視覺設計原則
- **純Console界面**：不使用背景圖片，採用純文字和邊框的經典Console設計
- **像素藝術角色**：所有怪物和玩家角色使用像素精靈圖呈現
- **覆蓋式特效**：技能動畫為像素風格的覆蓋效果，不影響角色精靈本身
- **經典配色**：使用傳統Console遊戲的配色方案（黑底白字、藍色邊框等）

### 1.3 核心設計理念
- **完全可配置**：怪物生成、屬性縮放、掉落系統全部通過CSV配置
- **動態平衡**：基於等級的屬性縮放確保遊戲難度曲線合理
- **策略深度**：通過技能標籤系統和狀態效果創造豐富的戰術組合
- **懷舊體驗**：保持經典RPG的操作感受和視覺風格

---

## 2. 核心遊戲循環

### 2.1 主要遊戲循環
```
戰鬥 → 獲得配置化獎勵 → 在隊伍頁面強化角色 → 組建最佳團隊 → 挑戰下一關卡 → 戰鬥
```

### 2.2 配置驅動的內容循環
```
關卡配置 → 怪物生成 → 掉落表計算 → 獎勵分發 → 角色成長 → 新策略探索
```

---

## 3. Console風格UI設計

### 3.1 整體界面架構
```
┌─────────────────────────────────────┐
│              Title Bar              │
├─────────────────────────────────────┤
│                                     │
│            Main Content             │
│         (Battle/Menu Area)          │
│                                     │
├─────────────────────────────────────┤
│            Status Bar               │
│     (HP/Gold/Level Info)           │
└─────────────────────────────────────┘
```

### 3.2 戰鬥界面Console風格設計
```
┌─────────────────────────────────────┐
│           BATTLE STAGE 1-5          │
├─────────────────────────────────────┤
│  [Player Team]        [Enemy Team]  │
│                                     │
│  🧙 Wizard Lv.10      👹 Orc Lv.8   │
│  HP: 150/150         HP: 120/140   │
│                                     │
│  ⚔️ Knight Lv.12      🐺 Wolf Lv.7  │
│  HP: 200/200         HP: 80/80     │
│                                     │
├─────────────────┬───────────────────┤
│   BATTLE LOG    │   ACTION MENU     │
│                 │                   │
│ > Wizard casts  │ ► ATTACK          │
│   Fireball!     │   SKILL           │
│ > 45 damage!    │   ITEM            │
│ > Orc attacks   │   CATCH           │
│ > 25 damage!    │   AUTO: ON        │
└─────────────────┴───────────────────┘
```

### 3.3 移動端觸控交互
- **垂直滑動**：在選單選項間移動光標
- **點擊確認**：選擇當前高亮選項
- **向右滑動**：進入子選單
- **向左滑動**：返回上級選單
- **長按**：顯示詳細信息

### 3.4 Console風格配色方案
```
主要配色：
- 背景色：#000000 (純黑)
- 主文字：#FFFFFF (純白)
- 邊框色：#0080FF (藍色)
- 選中項：#FFFF00 (黃色)
- HP條：#00FF00 (綠色) → #FF0000 (紅色)
- MP條：#0080FF (藍色)
```

---

## 4. 怪物生成系統

### 4.1 怪物生成演算法
```
怪物生成流程：
1. 讀取關卡配置中的怪物池定義
2. 根據稀有度過濾器篩選可用怪物
3. 在指定數量範圍內隨機決定生成數量
4. 從怪物池中隨機選擇怪物ID
5. 應用等級縮放係數計算最終屬性
6. 生成戰鬥怪物實例
```

### 4.2 怪物池配置支援
支援多種怪物池配置方式：
- **ID範圍**：`1001-1010` (從ID 1001到1010的所有怪物)
- **ID陣列**：`1001,1003,1005,1008` (指定的怪物ID列表)
- **混合配置**：`1001-1005,1010,1015-1020` (範圍和單獨ID的組合)

### 4.3 稀有度過濾機制
```
稀有度過濾器範例：
- "0,1"：只生成Common和Rare怪物
- "2-4"：只生成Epic到Mythic怪物
- "0,2,4"：只生成Common、Epic、Mythic怪物
```

### 4.4 等級縮放系統
```
屬性縮放公式：
最終屬性 = 基礎屬性 × (1 + (關卡等級 - 1) × 縮放係數)

範例：
基礎HP = 100
HP縮放係數 = 0.15
關卡等級 = 5
最終HP = 100 × (1 + (5-1) × 0.15) = 100 × 1.6 = 160
```

### 4.5 可配置縮放係數
每個屬性都有獨立的縮放係數：
- `HPScaling`：生命值縮放係數
- `AttackScaling`：攻擊力縮放係數
- `DefenseScaling`：防禦力縮放係數
- `SpeedScaling`：速度縮放係數
- `CritRateScaling`：暴擊率縮放係數
- `CritDamageScaling`：暴擊傷害縮放係數

---

## 5. 配置系統設計

### 5.1 關卡配置 (StageConfig.csv)
```csv
StageID,Level,Stage,IsBoss,MonsterPools,MonsterQuantityMin,MonsterQuantityMax,RarityFilter,DropTableID,BGM
1001,1,1,0,"1001-1005",2,3,"0,1",2001,bgm_battle
1002,1,2,0,"1001-1005,1010",2,4,"0,1,2",2002,bgm_battle
1010,1,10,1,"1020",1,1,"3",2010,bgm_boss
```

**字段說明**：
- `StageID`：關卡唯一識別碼
- `Level`：關卡等級（用於屬性縮放計算）
- `Stage`：關卡階段編號
- `IsBoss`：是否為Boss戰 (0=否, 1=是)
- `MonsterPools`：怪物池定義，支援範圍和陣列格式
- `MonsterQuantityMin/Max`：生成怪物數量範圍
- `RarityFilter`：稀有度過濾器
- `DropTableID`：關卡掉落表ID
- `BGM`：背景音樂ID

### 5.2 怪物配置 (MonsterConfig.csv)
```csv
MonsterID,Name,Rarity,BaseHP,BaseAttack,BaseDefense,BaseSpeed,BaseCritRate,BaseCritDamage,ActiveSkills,PassiveSkills,AIType,CatchRate,SpriteID
1001,Slime,0,80,15,8,12,0.05,1.5,"2001","3001","Aggressive",0.8,sprite_slime
1002,Goblin,1,120,25,15,18,0.08,1.6,"2002,2003","3002","Balanced",0.6,sprite_goblin
```

**字段說明**：
- `MonsterID`：怪物唯一識別碼
- `Rarity`：稀有度等級 (0=Common, 1=Rare, 2=Epic, 3=Legendary, 4=Mythic)
- `BaseHP/Attack/Defense/Speed`：基礎屬性值
- `ActiveSkills/PassiveSkills`：技能ID列表 (逗號分隔)
- `AIType`：AI行為類型
- `CatchRate`：基礎捕捉成功率
- `SpriteID`：角色精靈資源ID

### 5.3 等級縮放配置 (LevelScalingConfig.csv)
```csv
ScalingID,HPScaling,AttackScaling,DefenseScaling,SpeedScaling,CritRateScaling,CritDamageScaling,Description
default,0.15,0.12,0.10,0.08,0.02,0.05,Default scaling for normal monsters
boss,0.25,0.20,0.18,0.10,0.03,0.08,Enhanced scaling for boss monsters
```

### 5.4 技能配置 (SkillConfig.csv)
```csv
SkillID,Name,Type,TargetType,Cooldown,DamageMultiplier,FlatDamage,HealAmount,StatusEffects,Tags,AnimationID,Description
2001,Basic Attack,Active,SingleEnemy,0,1.0,0,0,,"Offensive,Physical",anim_slash,Basic physical attack
2002,Fireball,Active,SingleEnemy,3,1.5,20,0,"4001:3","Offensive,Magic,Fire",anim_fireball,Fire magic attack
```

**字段說明**：
- `Type`：技能類型 (Active/Passive)
- `TargetType`：目標類型 (Self/SingleEnemy/AllEnemies/SingleAlly/AllAllies/Random)
- `Cooldown`：冷卻回合數
- `StatusEffects`：附加狀態效果 "效果ID:持續回合"
- `Tags`：技能標籤 (逗號分隔)
- `AnimationID`：動畫效果ID

### 5.5 狀態效果配置 (StatusEffectConfig.csv)
```csv
StatusID,Name,Type,IsStackable,MaxStacks,TickDamage,TickHeal,StatModifiers,Tags,IconID,Description
4001,Burn,Debuff,1,5,15,0,,"Damage,Fire,DoT",icon_burn,Fire damage over time
4002,Stun,Control,0,1,0,0,"Speed:-1.0","Control,Physical",icon_stun,Cannot act for duration
```

### 5.6 掉落表配置 (DropTableConfig.csv)
```csv
DropTableID,ItemType,ItemID,BaseDropRate,MinQuantity,MaxQuantity,RarityWeight,LevelRequirement
2001,Gold,0,1.0,10,20,1.0,0
2001,Equipment,4001,0.3,1,1,1.0,0
2001,Skill,5001,0.1,1,1,1.0,0
```

**字段說明**：
- `DropTableID`：掉落表ID (對應關卡配置)
- `ItemType`：物品類型 (Gold/Equipment/Skill/Item)
- `BaseDropRate`：基礎掉落率 (0-1之間的小數)
- `RarityWeight`：稀有度權重

---

## 6. 標籤系統設計

### 6.1 配置驅動的標籤層級系統

#### 6.1.1 設計理念
標籤系統完全通過配置文件定義，提供：
- **靈活調整**：無需修改程式碼即可調整標籤關係
- **易於擴展**：新增標籤類型和層級關係
- **數據驅動**：支援複雜的標籤互動邏輯

#### 6.1.2 標籤層級結構
```
標籤層級範例：
Root
├── Damage
│   ├── Physical
│   │   ├── Slash
│   │   ├── Pierce
│   │   └── Blunt
│   └── Magic
│       ├── Fire
│       ├── Ice
│       ├── Lightning
│       ├── Holy
│       └── Dark
├── Effect
│   ├── Buff
│   ├── Debuff
│   └── Control
└── Function
    ├── Offensive
    ├── Defensive
    ├── Support
    └── Utility
```

### 6.2 標籤配置文件

#### 6.2.1 標籤定義配置 (TagDefinitionConfig.csv)
```csv
TagID,TagName,ParentTagID,Level,Category,Description,Color
1,Root,0,0,System,Root tag for all tags,#FFFFFF
2,Damage,1,1,Type,Damage dealing tags,#FF0000
3,Physical,2,2,Type,Physical damage type,#8B4513
4,Magic,2,2,Type,Magic damage type,#4169E1
8,Fire,4,3,Element,Fire magic damage,#FF4500
9,Ice,4,3,Element,Ice magic damage,#87CEEB
```

#### 6.2.2 標籤關係配置 (TagRelationConfig.csv)
```csv
RelationID,SourceTagID,TargetTagID,RelationType,Strength,Condition,Description
1,8,9,Opposite,1.0,,Fire and Ice are opposite elements
2,8,9,Weakness,0.5,Target has Ice resistance,Fire is weak against Ice resistance
3,10,4,Amplify,1.2,Target is wet,Lightning amplified by water
```

#### 6.2.3 標籤互動規則配置 (TagInteractionConfig.csv)
```csv
InteractionID,TriggerTags,TargetTags,InteractionType,Effect,Value,Duration,Description
1,"Fire,Magic","Ice,Resistance",Damage,Reduce,0.5,0,Fire magic reduced by ice resistance
2,"Lightning,Magic","Water,Wet",Damage,Amplify,1.5,0,Lightning amplified when target is wet
```

---

## 7. 像素繪製特效系統

### 7.1 像素繪製系統理念

#### 7.1.1 設計目標
創建一個可以在運行時動態繪製像素圖像的系統：
- **動態特效**：根據技能參數動態生成視覺效果
- **程序化動畫**：通過程式碼控制像素繪製創造動畫
- **記憶體效率**：避免儲存大量預製的特效精靈
- **高度自定義**：可以根據遊戲狀態調整特效外觀

#### 7.1.2 技術特點
- **像素級控制**：可以控制每個像素的顏色和透明度
- **分層渲染**：支援多層像素繪製，創造複雜效果
- **動畫支援**：支援逐幀動畫和程序化動畫
- **效能優化**：使用Texture2D和GPU渲染優化性能

### 7.2 特效配置系統

#### 7.2.1 特效定義配置 (PixelEffectConfig.csv)
```csv
EffectID,EffectName,Type,Width,Height,Duration,FrameRate,LayerCount,BlendMode,Description
1,Fireball,Projectile,32,32,1.0,12,3,Additive,Fire projectile effect
2,Explosion,Area,64,64,0.8,15,4,Additive,Explosion area effect
3,Heal_Aura,Buff,48,48,2.0,8,2,Alpha,Healing aura effect
```

#### 7.2.2 像素繪製指令配置 (PixelDrawCommandConfig.csv)
```csv
CommandID,EffectID,Layer,Frame,CommandType,Parameters,Color,Description
1,1,0,0,FillCircle,"16,16,8",#FF4500,Fire core circle
2,1,0,0,DrawCircle,"16,16,12,2",#FF6347,Fire outer ring
3,1,1,0,DrawParticles,"16,16,5,3",#FFFF00,Fire sparks
```

### 7.3 繪製指令類型
```
基礎形狀：
- SetPixel: 設置單個像素
- DrawLine: 繪製直線
- DrawCircle: 繪製圓形
- FillCircle: 填充圓形
- DrawRect: 繪製矩形
- FillRect: 填充矩形

進階形狀：
- DrawRing: 繪製環形
- DrawArc: 繪製弧形
- DrawPolygon: 繪製多邊形

特效專用：
- DrawParticles: 繪製粒子
- DrawSparkles: 繪製閃光
- DrawGradientCircle: 漸變圓形
- DrawNoise: 噪點效果
- DrawWave: 波浪效果

動畫相關：
- AnimateMove: 移動動畫
- AnimateScale: 縮放動畫
- AnimateRotate: 旋轉動畫
- AnimateFade: 淡化動畫
- AnimateColor: 顏色動畫
```

---

## 8. 技術架構

### 8.1 整體架構模式
採用 **Manager Pattern** + **Event-Driven Architecture**：

```
GameManager (遊戲總控制器)
├── DataManager (數據管理)
├── BattleManager (戰鬥管理)
├── PlayerManager (玩家數據管理)
├── UIManager (界面管理)
├── TagManager (標籤系統管理)
├── PixelEffectManager (像素特效管理)
└── ConfigManager (配置管理)
```

### 8.2 數據管理架構
```
數據層級：
CSV Files (配置數據)
    ↓
ConfigManager (配置管理器)
    ↓
Manager Classes (業務邏輯)
    ↓
MonoBehaviour Components (遊戲對象)
```

### 8.3 怪物生成系統架構
```csharp
public class MonsterSpawnManager : MonoBehaviour
{
    public List<BattleMonster> GenerateMonstersForStage(StageConfigData stageConfig)
    {
        // 1. 解析怪物池配置
        List<int> availableMonsterIDs = ParseMonsterPools(stageConfig.MonsterPools);
        
        // 2. 應用稀有度過濾
        availableMonsterIDs = ApplyRarityFilter(availableMonsterIDs, stageConfig.RarityFilter);
        
        // 3. 決定生成數量
        int spawnCount = Random.Range(stageConfig.MonsterQuantityMin, stageConfig.MonsterQuantityMax + 1);
        
        // 4. 隨機選擇並生成怪物
        // 5. 應用等級縮放
        
        return monsters;
    }
}
```

### 8.4 像素繪製引擎架構
```csharp
public class PixelDrawEngine : MonoBehaviour
{
    public PixelCanvas CreateCanvas(int width, int height);
    public void ExecuteDrawCommand(PixelCanvas canvas, PixelDrawCommand command);
    public void ReturnCanvas(PixelCanvas canvas);
}

public class PixelCanvas
{
    public void SetPixel(int x, int y, Color color);
    public Color GetPixel(int x, int y);
    public void ApplyChanges();
    public void BlendPixel(int x, int y, Color color, BlendMode blendMode);
}
```

---

## 9. 實作優先級

### 9.1 第一階段：配置系統基礎 (週1-2)
1. **CSV配置讀取器**：實作所有配置文件的解析和載入
2. **怪物生成系統**：實作基於配置的怪物生成演算法
3. **等級縮放系統**：實作屬性縮放計算
4. **基礎數據結構**：定義所有核心數據類別

### 9.2 第二階段：Console風格UI (週3-4)
1. **Console界面框架**：實作純文字風格的界面系統
2. **像素精靈顯示**：實作角色精靈的顯示和管理
3. **觸控輸入適配**：將觸控手勢轉換為Console操作
4. **基礎戰鬥界面**：實作戰鬥畫面的Console風格佈局

### 9.3 第三階段：標籤系統 (週5)
1. **標籤配置系統**：實作CSV配置讀取和標籤層級建構
2. **標籤管理器**：實作基礎的標籤查詢和關係判斷
3. **標籤互動計算**：實作基於配置的標籤互動邏輯

### 9.4 第四階段：像素繪製特效 (週6-7)
1. **像素畫布系統**：實作基礎的像素繪製和紋理管理
2. **繪製指令系統**：實作基本的繪製指令
3. **特效動畫器**：實作基礎的動畫播放和幀管理
4. **進階繪製功能**：實作粒子、漸變、噪點等特殊效果

### 9.5 第五階段：戰鬥系統整合 (週8-9)
1. **戰鬥流程管理**：實作完整的戰鬥邏輯
2. **技能系統**：實作基於標籤的技能系統
3. **狀態效果系統**：實作完整的Buff/Debuff機制
4. **AI系統**：實作怪物和自動戰鬥AI

### 9.6 第六階段：完整遊戲體驗 (週10)
1. **隊伍管理**：實作完整的隊伍編輯功能
2. **掉落系統**：實作基於關卡的掉落機制
3. **音效和反饋**：添加Console風格的音效反饋
4. **性能優化**：優化像素渲染和特效系統

---

## 總結

這個設計文檔提供了一個完整的Console風格像素RPG遊戲架構，具備以下核心特色：

### 技術特色
- **完全可配置**：所有遊戲內容通過CSV文件配置
- **靈活的怪物生成**：支援ID範圍、陣列、稀有度過濾的複雜生成規則
- **動態屬性縮放**：基於等級的可配置屬性縮放系統
- **純Console風格**：經典的文字界面配合現代觸控操作

### 視覺特色
- **像素藝術風格**：所有角色使用像素精靈呈現
- **動態像素特效**：運行時繪製的像素特效系統
- **經典配色**：傳統Console遊戲的視覺風格
- **像素完美渲染**：確保像素藝術的清晰呈現

### 系統特色
- **配置驅動標籤**：標籤系統完全通過配置文件定義
- **關卡級掉落**：掉落表與關卡綁定而非怪物綁定
- **無魔力系統**：簡化的戰鬥機制專注於策略性
- **移動端優化**：Console風格界面適配觸控操作

這個架構為創建一個既有懷舊感又具現代可玩性的RPG遊戲提供了堅實的基礎。

---

## 附錄A：詳細配置文件範例

### A.1 完整的怪物配置範例
```csv
MonsterID,Name,Rarity,BaseHP,BaseAttack,BaseDefense,BaseSpeed,BaseCritRate,BaseCritDamage,ActiveSkills,PassiveSkills,AIType,CatchRate,SpriteID
1001,Green Slime,0,80,15,8,12,0.05,1.5,"2001","3001","Aggressive",0.8,sprite_slime_green
1002,Blue Slime,0,85,12,10,15,0.05,1.5,"2001","3002","Defensive",0.8,sprite_slime_blue
1003,Goblin Scout,1,120,25,15,18,0.08,1.6,"2002,2003","3003","Balanced",0.6,sprite_goblin_scout
1004,Goblin Warrior,1,140,30,20,16,0.10,1.7,"2004,2005","3004","Aggressive",0.5,sprite_goblin_warrior
1005,Orc Berserker,2,200,45,25,20,0.15,1.9,"2006,2007,2008","3005,3006","Aggressive",0.3,sprite_orc_berserker
1010,Forest Dragon,3,500,80,60,25,0.20,2.2,"2010,2011,2012","3010,3011","Aggressive",0.1,sprite_dragon_forest
1020,Ancient Golem,4,800,100,120,8,0.15,2.5,"2020,2021","3020,3021,3022","Defensive",0.05,sprite_golem_ancient
```

### A.2 完整的技能配置範例
```csv
SkillID,Name,Type,TargetType,Cooldown,DamageMultiplier,FlatDamage,HealAmount,StatusEffects,Tags,AnimationID,Description
2001,Basic Attack,Active,SingleEnemy,0,1.0,0,0,,"Offensive,Physical",anim_slash,Basic physical attack
2002,Fireball,Active,SingleEnemy,3,1.5,20,0,"4001:3","Offensive,Magic,Fire",anim_fireball,Hurls a ball of fire at enemy
2003,Ice Shard,Active,SingleEnemy,3,1.3,15,0,"4002:2","Offensive,Magic,Ice",anim_ice_shard,Launches sharp ice at enemy
2004,Thunder Strike,Active,SingleEnemy,4,1.8,25,0,"4003:1","Offensive,Magic,Lightning",anim_thunder,Lightning attack from above
2005,Heal,Active,SingleAlly,4,0,0,50,,"Support,Magic,Healing",anim_heal,Restore HP to ally
2006,Group Heal,Active,AllAllies,6,0,0,30,,"Support,Magic,Healing",anim_group_heal,Heal all allies
2007,Shield Bash,Active,SingleEnemy,2,0.8,0,0,"4004:2","Offensive,Physical,Control",anim_bash,Attack with chance to stun
2008,Berserker Rage,Active,Self,8,0,0,0,"4005:5","Buff,Physical",anim_rage,Increase attack but reduce defense
2010,Dragon Breath,Active,AllEnemies,5,1.2,30,0,"4001:3","Offensive,Magic,Fire",anim_dragon_breath,Fire breath hits all enemies
2011,Wing Gust,Active,AllEnemies,3,0.6,0,0,"4006:2","Offensive,Physical,Control",anim_wing_gust,Wind attack that may confuse
2012,Intimidate,Active,AllEnemies,6,0,0,0,"4007:4","Debuff,Control",anim_intimidate,Reduce enemy attack power
2020,Stone Slam,Active,SingleEnemy,3,2.0,40,0,"4008:1","Offensive,Physical",anim_stone_slam,Powerful earth attack
2021,Earthquake,Active,AllEnemies,8,1.5,20,0,"4008:2","Offensive,Physical",anim_earthquake,Ground attack hits all enemies
3001,Slippery,Passive,Self,0,0,0,0,"4009:0","Defensive,Physical",none,Chance to avoid physical attacks
3002,Regeneration,Passive,Self,0,0,0,5,,"Defensive,Healing",none,Slowly recover HP each turn
3003,Keen Eye,Passive,Self,0,0,0,0,"4010:0","Offensive,Physical",none,Increased critical hit rate
3004,Tough Skin,Passive,Self,0,0,0,0,"4011:0","Defensive,Physical",none,Reduced physical damage taken
3005,Battle Frenzy,Passive,Self,0,0,0,0,"4012:0","Offensive,Physical",none,Attack increases when HP is low
3006,Bloodthirst,Passive,Self,0,0,0,0,"4013:0","Offensive,Physical",none,Heal when dealing critical damage
3010,Dragon Scales,Passive,Self,0,0,0,0,"4014:0","Defensive,Magic",none,Reduced magic damage taken
3011,Ancient Wisdom,Passive,Self,0,0,0,0,"4015:0","Support,Magic",none,Increased experience gain
3020,Stone Body,Passive,Self,0,0,0,0,"4016:0","Defensive,Physical",none,Immunity to status effects
3021,Earth Affinity,Passive,Self,0,0,0,0,"4017:0","Offensive,Magic",none,Increased earth magic damage
3022,Immovable,Passive,Self,0,0,0,0,"4018:0","Defensive,Control",none,Cannot be moved or knocked down
```

### A.3 完整的狀態效果配置範例
```csv
StatusID,Name,Type,IsStackable,MaxStacks,TickDamage,TickHeal,StatModifiers,Tags,IconID,Description
4001,Burn,Debuff,1,5,15,0,,"Damage,Fire,DoT",icon_burn,Fire damage over time
4002,Freeze,Control,0,1,0,0,"Speed:-0.5","Control,Ice",icon_freeze,Reduced speed and action delay
4003,Shock,Debuff,1,3,10,0,"Defense:-0.2","Damage,Lightning,DoT",icon_shock,Lightning damage and defense reduction
4004,Stun,Control,0,1,0,0,"Speed:-1.0","Control,Physical",icon_stun,Cannot act for duration
4005,Rage,Buff,0,1,0,0,"Attack:+0.5,Defense:-0.3","Buff,Physical",icon_rage,Increased attack but reduced defense
4006,Confusion,Debuff,0,1,0,0,"Accuracy:-0.3","Control,Mental",icon_confusion,Reduced accuracy and may attack allies
4007,Fear,Debuff,0,1,0,0,"Attack:-0.4,Speed:-0.2","Debuff,Mental",icon_fear,Reduced combat effectiveness
4008,Earth Bind,Control,0,1,0,0,"Speed:-0.8","Control,Earth",icon_earth_bind,Severely reduced movement
4009,Evasion Up,Buff,0,1,0,0,"Evasion:+0.3","Buff,Physical",icon_evasion,Increased dodge chance
4010,Critical Up,Buff,0,1,0,0,"CriticalRate:+0.2","Buff,Physical",icon_crit_up,Increased critical hit rate
4011,Defense Up,Buff,0,1,0,0,"Defense:+0.3","Buff,Physical",icon_def_up,Increased defense
4012,Frenzy,Buff,1,3,0,0,"Attack:+0.1","Buff,Physical",icon_frenzy,Stacking attack bonus when HP is low
4013,Lifesteal,Buff,0,1,0,0,"Lifesteal:+0.2","Buff,Physical",icon_lifesteal,Heal when dealing damage
4014,Magic Resist,Buff,0,1,0,0,"MagicResistance:+0.4","Buff,Magic",icon_magic_resist,Reduced magic damage taken
4015,Wisdom,Buff,0,1,0,0,"ExpGain:+0.5","Buff,Mental",icon_wisdom,Increased experience gain
4016,Stone Skin,Buff,0,1,0,0,"StatusImmunity:+1.0","Buff,Earth",icon_stone_skin,Immunity to status effects
4017,Earth Power,Buff,0,1,0,0,"EarthDamage:+0.3","Buff,Earth",icon_earth_power,Increased earth damage
4018,Immobilize,Control,0,1,0,0,"Movement:-1.0","Control,Physical",icon_immobilize,Cannot move or be moved
```

---

## 附錄B：技術實作細節

### B.1 怪物生成演算法詳細實作
```csharp
public class MonsterSpawnManager : MonoBehaviour
{
    private List<int> ParseMonsterPools(string monsterPools)
    {
        List<int> result = new List<int>();
        string[] pools = monsterPools.Split(',');

        foreach (string pool in pools)
        {
            pool = pool.Trim();
            if (pool.Contains('-'))
            {
                // 處理範圍格式 "1001-1010"
                string[] range = pool.Split('-');
                if (range.Length == 2 &&
                    int.TryParse(range[0], out int start) &&
                    int.TryParse(range[1], out int end))
                {
                    for (int i = start; i <= end; i++)
                    {
                        result.Add(i);
                    }
                }
            }
            else
            {
                // 處理單獨ID "1005"
                if (int.TryParse(pool, out int id))
                {
                    result.Add(id);
                }
            }
        }

        return result;
    }

    private List<int> ApplyRarityFilter(List<int> monsterIDs, string rarityFilter)
    {
        if (string.IsNullOrEmpty(rarityFilter))
            return monsterIDs;

        List<int> allowedRarities = new List<int>();
        string[] filters = rarityFilter.Split(',');

        foreach (string filter in filters)
        {
            string trimmedFilter = filter.Trim();
            if (trimmedFilter.Contains('-'))
            {
                // 處理範圍 "2-4"
                string[] range = trimmedFilter.Split('-');
                if (range.Length == 2 &&
                    int.TryParse(range[0], out int start) &&
                    int.TryParse(range[1], out int end))
                {
                    for (int i = start; i <= end; i++)
                    {
                        allowedRarities.Add(i);
                    }
                }
            }
            else
            {
                // 處理單獨稀有度 "0"
                if (int.TryParse(trimmedFilter, out int rarity))
                {
                    allowedRarities.Add(rarity);
                }
            }
        }

        return monsterIDs.Where(id =>
        {
            var monsterData = DataManager.Instance.GetMonster(id);
            return monsterData != null && allowedRarities.Contains(monsterData.Rarity);
        }).ToList();
    }

    private BattleMonster CreateScaledMonster(int monsterID, int stageLevel)
    {
        MonsterConfigData baseConfig = DataManager.Instance.GetMonster(monsterID);
        if (baseConfig == null) return null;

        LevelScalingConfig scalingConfig = DataManager.Instance.GetScalingConfig("default");
        if (baseConfig.Rarity >= 3) // Boss monsters use different scaling
        {
            scalingConfig = DataManager.Instance.GetScalingConfig("boss");
        }

        BattleMonster monster = new BattleMonster();
        monster.MonsterID = monsterID;
        monster.Name = baseConfig.Name;
        monster.Rarity = baseConfig.Rarity;
        monster.Level = stageLevel;

        // 應用等級縮放
        float hpMultiplier = 1f + (stageLevel - 1) * scalingConfig.HPScaling;
        float attackMultiplier = 1f + (stageLevel - 1) * scalingConfig.AttackScaling;
        float defenseMultiplier = 1f + (stageLevel - 1) * scalingConfig.DefenseScaling;
        float speedMultiplier = 1f + (stageLevel - 1) * scalingConfig.SpeedScaling;

        monster.MaxHP = Mathf.RoundToInt(baseConfig.BaseHP * hpMultiplier);
        monster.Attack = Mathf.RoundToInt(baseConfig.BaseAttack * attackMultiplier);
        monster.Defense = Mathf.RoundToInt(baseConfig.BaseDefense * defenseMultiplier);
        monster.Speed = Mathf.RoundToInt(baseConfig.BaseSpeed * speedMultiplier);

        monster.CriticalRate = Mathf.Clamp01(baseConfig.BaseCritRate +
            (stageLevel - 1) * scalingConfig.CritRateScaling);
        monster.CriticalDamage = baseConfig.BaseCritDamage +
            (stageLevel - 1) * scalingConfig.CritDamageScaling;

        monster.CurrentHP = monster.MaxHP;

        // 載入技能
        monster.ActiveSkills = LoadSkills(baseConfig.ActiveSkills);
        monster.PassiveSkills = LoadSkills(baseConfig.PassiveSkills);

        // 設置AI類型
        monster.AIType = baseConfig.AIType;
        monster.CatchRate = baseConfig.CatchRate;
        monster.SpriteID = baseConfig.SpriteID;

        return monster;
    }
}
```

### B.2 像素繪製引擎核心實作
```csharp
public class PixelDrawEngine : MonoBehaviour
{
    private void ExecuteDrawLine(PixelCanvas canvas, PixelDrawCommand command)
    {
        int x1 = command.Parameters[0];
        int y1 = command.Parameters[1];
        int x2 = command.Parameters[2];
        int y2 = command.Parameters[3];
        int thickness = command.Parameters.Length > 4 ? command.Parameters[4] : 1;

        // Bresenham's line algorithm
        int dx = Mathf.Abs(x2 - x1);
        int dy = Mathf.Abs(y2 - y1);
        int sx = x1 < x2 ? 1 : -1;
        int sy = y1 < y2 ? 1 : -1;
        int err = dx - dy;

        int x = x1, y = y1;

        while (true)
        {
            // 繪製粗線
            for (int tx = -thickness/2; tx <= thickness/2; tx++)
            {
                for (int ty = -thickness/2; ty <= thickness/2; ty++)
                {
                    int pixelX = x + tx;
                    int pixelY = y + ty;
                    if (canvas.IsValidPixel(pixelX, pixelY))
                    {
                        canvas.BlendPixel(pixelX, pixelY, command.Color, command.BlendMode);
                    }
                }
            }

            if (x == x2 && y == y2) break;

            int e2 = 2 * err;
            if (e2 > -dy)
            {
                err -= dy;
                x += sx;
            }
            if (e2 < dx)
            {
                err += dx;
                y += sy;
            }
        }
    }

    private void ExecuteDrawGradientCircle(PixelCanvas canvas, PixelDrawCommand command)
    {
        int centerX = command.Parameters[0];
        int centerY = command.Parameters[1];
        int radius = command.Parameters[2];

        for (int y = -radius; y <= radius; y++)
        {
            for (int x = -radius; x <= radius; x++)
            {
                float distance = Mathf.Sqrt(x * x + y * y);
                if (distance <= radius)
                {
                    int pixelX = centerX + x;
                    int pixelY = centerY + y;

                    if (canvas.IsValidPixel(pixelX, pixelY))
                    {
                        // 計算漸變
                        float alpha = 1f - (distance / radius);
                        Color gradientColor = command.Color;
                        gradientColor.a *= alpha;

                        canvas.BlendPixel(pixelX, pixelY, gradientColor, command.BlendMode);
                    }
                }
            }
        }
    }

    private void ExecuteDrawNoise(PixelCanvas canvas, PixelDrawCommand command)
    {
        int x = command.Parameters[0];
        int y = command.Parameters[1];
        int width = command.Parameters[2];
        int height = command.Parameters[3];
        float density = command.Parameters.Length > 4 ? command.Parameters[4] / 100f : 0.5f;

        for (int py = y; py < y + height; py++)
        {
            for (int px = x; px < x + width; px++)
            {
                if (canvas.IsValidPixel(px, py) && Random.value < density)
                {
                    Color noiseColor = command.Color;
                    noiseColor.a *= Random.Range(0.3f, 1f);
                    canvas.BlendPixel(px, py, noiseColor, command.BlendMode);
                }
            }
        }
    }

    private void ExecuteDrawWave(PixelCanvas canvas, PixelDrawCommand command)
    {
        int x = command.Parameters[0];
        int y = command.Parameters[1];
        int width = command.Parameters[2];
        int height = command.Parameters[3];
        float frequency = command.Parameters.Length > 4 ? command.Parameters[4] / 10f : 1f;
        float amplitude = command.Parameters.Length > 5 ? command.Parameters[5] : height / 4f;

        for (int px = x; px < x + width; px++)
        {
            float waveY = y + height/2 + Mathf.Sin((px - x) * frequency * Mathf.PI / width) * amplitude;
            int pixelY = Mathf.RoundToInt(waveY);

            if (canvas.IsValidPixel(px, pixelY))
            {
                canvas.BlendPixel(px, pixelY, command.Color, command.BlendMode);
            }
        }
    }
}
```

---

## 附錄C：性能優化指南

### C.1 像素渲染優化
1. **批次處理**：將多個繪製指令合併為單次紋理更新
2. **對象池**：重用PixelCanvas和特效GameObject
3. **LOD系統**：根據距離調整特效複雜度
4. **異步處理**：大型特效使用協程分幀處理

### C.2 記憶體管理
1. **紋理壓縮**：使用適當的紋理格式
2. **垃圾回收**：避免頻繁的記憶體分配
3. **資源卸載**：及時釋放不需要的特效資源

### C.3 配置系統優化
1. **數據緩存**：常用配置數據保存在記憶體中
2. **延遲載入**：按需載入配置文件
3. **數據壓縮**：使用二進制格式存儲大型配置

這個完整的設計文檔為PetingGame提供了詳盡的開發指南，涵蓋了從基礎架構到具體實作的所有重要方面。
